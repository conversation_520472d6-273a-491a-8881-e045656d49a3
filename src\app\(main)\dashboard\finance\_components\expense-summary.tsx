"use client";

import { Coffee, Building2, Smartphone } from "lucide-react";
import { Label, PolarRadiusAxis, RadialBar, RadialBarChart } from "recharts";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Separator } from "@/components/ui/separator";

import { sectorInvestmentData } from "./mongolia-market-data";

const chartData = [{
  period: "current-year",
  fnb: sectorInvestmentData[0].amount,
  retail: sectorInvestmentData[1].amount,
  realestate: sectorInvestmentData[2].amount
}];

const chartConfig = {
  fnb: {
    label: "F&B Sector",
    color: "var(--chart-1)",
  },
  retail: {
    label: "Retail Sector",
    color: "var(--chart-2)",
  },
  realestate: {
    label: "Real Estate",
    color: "var(--chart-3)",
  },
} satisfies ChartConfig;

export function ExpenseSummary() {
  const totalInvestments = chartData.length ? chartData[0].fnb + chartData[0].retail + chartData[0].realestate : 0;
  return (
    <Card>
      <CardHeader>
        <CardTitle>Sector Investment Distribution</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Separator />

        <div className="h-32">
          <ChartContainer config={chartConfig}>
            <RadialBarChart
              margin={{ left: 0, right: 0, top: 0, bottom: 0 }}
              data={chartData}
              endAngle={180}
              innerRadius={80}
              outerRadius={130}
            >
              <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
              <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
                <Label
                  content={({ viewBox }) => {
                    if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                      return (
                        <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle">
                          <tspan
                            x={viewBox.cx}
                            y={(viewBox.cy ?? 0) - 16}
                            className="fill-foreground text-2xl font-bold tabular-nums"
                          >
                            ₮{(totalInvestments / 1000000).toFixed(0)}M
                          </tspan>
                          <tspan x={viewBox.cx} y={(viewBox.cy ?? 0) + 4} className="fill-muted-foreground">
                            Invested
                          </tspan>
                        </text>
                      );
                    }
                  }}
                />
              </PolarRadiusAxis>
              <RadialBar
                dataKey="realestate"
                stackId="a"
                cornerRadius={4}
                fill="var(--color-realestate)"
                className="stroke-card stroke-4"
              />
              <RadialBar
                dataKey="retail"
                stackId="a"
                cornerRadius={4}
                fill="var(--color-retail)"
                className="stroke-card stroke-4"
              />
              <RadialBar
                dataKey="fnb"
                stackId="a"
                cornerRadius={4}
                fill="var(--color-fnb)"
                className="stroke-card stroke-4"
              />
            </RadialBarChart>
          </ChartContainer>
        </div>
        <Separator />
        <div className="flex justify-between gap-4">
          <div className="flex flex-1 flex-col items-center space-y-2">
            <div className="bg-muted flex size-10 items-center justify-center rounded-full">
              <Coffee className="stroke-chart-1 size-5" />
            </div>
            <div className="space-y-0.5 text-center">
              <p className="text-muted-foreground text-xs uppercase">F&B</p>
              <p className="font-medium tabular-nums">₮{(chartData[0].fnb / 1000000).toFixed(0)}M</p>
            </div>
          </div>
          <Separator orientation="vertical" className="!h-auto" />
          <div className="flex flex-1 flex-col items-center space-y-2">
            <div className="bg-muted flex size-10 items-center justify-center rounded-full">
              <Building2 className="stroke-chart-2 size-5" />
            </div>
            <div className="space-y-0.5 text-center">
              <p className="text-muted-foreground text-xs uppercase">Retail</p>
              <p className="font-medium tabular-nums">₮{(chartData[0].retail / 1000000).toFixed(0)}M</p>
            </div>
          </div>
          <Separator orientation="vertical" className="!h-auto" />
          <div className="flex flex-1 flex-col items-center space-y-2">
            <div className="bg-muted flex size-10 items-center justify-center rounded-full">
              <Smartphone className="stroke-chart-3 size-5" />
            </div>
            <div className="space-y-0.5 text-center">
              <p className="text-muted-foreground text-xs uppercase">Real Estate</p>
              <p className="font-medium tabular-nums">₮{(chartData[0].realestate / 1000000).toFixed(0)}M</p>
            </div>
          </div>
        </div>
        <span className="text-muted-foreground text-xs tabular-nums">
          Total sector investments: ₮{(totalInvestments / 1000000).toFixed(0)}M MNT
        </span>
      </CardContent>
    </Card>
  );
}
