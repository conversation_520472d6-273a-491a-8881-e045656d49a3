import { NextRequest, NextResponse } from "next/server";
import OpenAI from "openai";

// Initialize OpenAI with API key from environment variables
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Rate limiting configuration
const RATE_LIMIT_CONFIG = {
  maxRequests: 10,
  windowMs: 60000, // 1 minute
  maxMessageLength: 1000,
  maxTokens: 500,
};

// In-memory rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Input validation and sanitization
function validateAndSanitizeInput(input: any): { isValid: boolean; error?: string; sanitized?: any } {
  if (!input || typeof input !== 'object') {
    return { isValid: false, error: 'Invalid request body' };
  }

  const { message, context, timestamp } = input;

  // Validate message
  if (!message || typeof message !== 'string') {
    return { isValid: false, error: 'Message is required and must be a string' };
  }

  if (message.length > RATE_LIMIT_CONFIG.maxMessageLength) {
    return { isValid: false, error: `Message too long (max ${RATE_LIMIT_CONFIG.maxMessageLength} characters)` };
  }

  if (message.trim().length < 2) {
    return { isValid: false, error: 'Message too short (min 2 characters)' };
  }

  // Basic XSS and injection prevention
  const dangerousPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /data:text\/html/gi,
    /vbscript:/gi,
    /on\w+\s*=/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(message)) {
      return { isValid: false, error: 'Invalid characters detected in message' };
    }
  }

  // Validate context
  if (context && typeof context !== 'string') {
    return { isValid: false, error: 'Context must be a string' };
  }

  // Validate timestamp
  if (timestamp && (typeof timestamp !== 'number' || timestamp < 0)) {
    return { isValid: false, error: 'Invalid timestamp' };
  }

  return {
    isValid: true,
    sanitized: {
      message: message.trim(),
      context: context || 'coffee_shop_market_ulaanbaatar',
      timestamp: timestamp || Date.now(),
    },
  };
}

// Rate limiting function
function checkRateLimit(clientId: string): { allowed: boolean; error?: string } {
  const now = Date.now();
  const clientData = rateLimitStore.get(clientId);

  if (!clientData || now - clientData.resetTime > RATE_LIMIT_CONFIG.windowMs) {
    rateLimitStore.set(clientId, { count: 1, resetTime: now });
    return { allowed: true };
  }

  if (clientData.count >= RATE_LIMIT_CONFIG.maxRequests) {
    return {
      allowed: false,
      error: `Rate limit exceeded. Maximum ${RATE_LIMIT_CONFIG.maxRequests} requests per minute.`
    };
  }

  clientData.count++;
  return { allowed: true };
}

// Get client identifier for rate limiting
function getClientId(request: NextRequest): string {
  // Use IP address as client identifier
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
  return ip;
}

// Coffee shop market data context for the AI
const MARKET_DATA_CONTEXT = `
You are an AI assistant for Oyu-Data-AI, a market intelligence platform focused on Mongolia. You have access to comprehensive coffee shop market data for Ulaanbaatar. Here's the key data you should reference:

DISTRICTS AND COFFEE SHOP DISTRIBUTION:
- Sukhbaatar: 15 coffee shops, high competition, avg rent ₮46,500/sqm
- Chingeltei: 22 coffee shops, very high competition, avg rent ₮42,000/sqm  
- Bayanzurkh: 18 coffee shops, medium competition, avg rent ₮26,500/sqm
- Khan-Uul: 12 coffee shops, medium competition, avg rent ₮32,000/sqm
- Bayangol: 14 coffee shops, high competition, avg rent ₮32,500/sqm
- Songinokhairkhan: 8 coffee shops, low competition, avg rent ₮19,000/sqm

COFFEE SHOP TYPES:
- International Chains: 8 shops, 45% market share, avg price ₮8,850
- Local Chains: 12 shops, 35% market share, avg price ₮7,200  
- Independent: 25 shops, 20% market share, avg price ₮5,800

KEY MARKET INSIGHTS:
- Total market size: ₮1.2B annually
- Average coffee price: ₮6,800
- Market growth: +15% YoY
- Total shops: 89 across all districts
- Peak hours: 8-10 AM, 2-4 PM
- Seasonal trend: Summer peak (+25%), Winter low (-15%)

INVESTMENT OPPORTUNITIES:
- Songinokhairkhan: High opportunity, low competition, growing population, 85% growth potential
- Bayanzurkh: Medium opportunity, large population, moderate competition, 70% growth potential
- Khan-Uul: Medium opportunity, diplomatic area, higher income, 75% growth potential

HIGH FOOT TRAFFIC LOCATIONS:
- Government Square: Very high traffic (score 95), 3 shops
- Central Tower Area: Very high traffic (score 92), 4 shops
- Peace Avenue: High traffic (score 85), 6 shops

Always provide specific data points, district names, and actionable insights. Be concise but informative. Focus on helping users make data-driven business decisions.
`;

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      console.error("OpenAI API key is not configured");
      return NextResponse.json(
        { error: "Service temporarily unavailable. Please try again later." },
        { status: 503 }
      );
    }

    // Get client identifier for rate limiting
    const clientId = getClientId(request);

    // Check rate limiting
    const rateLimitResult = checkRateLimit(clientId);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: rateLimitResult.error },
        { status: 429 }
      );
    }

    // Parse and validate request body
    let requestBody;
    try {
      requestBody = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    // Validate and sanitize input
    const validation = validateAndSanitizeInput(requestBody);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    const { message, context } = validation.sanitized!;

    // Create OpenAI completion with timeout and error handling
    const completion = await Promise.race([
      openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: MARKET_DATA_CONTEXT,
          },
          {
            role: "user",
            content: message,
          },
        ],
        max_tokens: RATE_LIMIT_CONFIG.maxTokens,
        temperature: 0.7,
        user: clientId, // For OpenAI usage tracking
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 30000)
      )
    ]);

    const responseMessage = completion.choices[0]?.message?.content;

    if (!responseMessage) {
      throw new Error('No response from AI model');
    }

    // Log successful request (without sensitive data)
    const processingTime = Date.now() - startTime;
    console.log(`Chat request processed successfully in ${processingTime}ms for client ${clientId.substring(0, 8)}...`);

    return NextResponse.json({
      message: responseMessage.trim(),
      timestamp: Date.now(),
    });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    const clientId = getClientId(request);

    // Log error (without sensitive data)
    console.error(`Chat request failed after ${processingTime}ms for client ${clientId.substring(0, 8)}...:`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    // Determine error type and appropriate response
    let statusCode = 500;
    let errorMessage = "I'm currently experiencing technical difficulties. Please try again later.";

    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        statusCode = 408;
        errorMessage = "Request timed out. Please try again with a shorter message.";
      } else if (error.message.includes('rate limit') || error.message.includes('quota')) {
        statusCode = 429;
        errorMessage = "Service is currently busy. Please try again in a moment.";
      } else if (error.message.includes('API key')) {
        statusCode = 503;
        errorMessage = "Service temporarily unavailable. Please try again later.";
      } else if (error.message.includes('No response')) {
        statusCode = 502;
        errorMessage = "Unable to generate response. Please try rephrasing your question.";
      }
    }

    // Provide fallback responses for common questions when possible
    try {
      const requestBody = await request.clone().json();
      const userMessage = requestBody?.message?.toLowerCase() || "";

      if (statusCode === 500 && userMessage) {
        if (userMessage.includes("competition") && userMessage.includes("low")) {
          return NextResponse.json({
            message: "Based on our data, **Songinokhairkhan district** has the lowest competition with only 8 coffee shops and low competition density. This district offers high investment opportunity with 85% growth potential and affordable rent at ₮19,000/sqm.",
            fallback: true,
          });
        }

        if (userMessage.includes("rent") && userMessage.includes("sukhbaatar")) {
          return NextResponse.json({
            message: "The average rent in **Sukhbaatar district** is **₮46,500 per square meter**. This is one of the higher-rent districts due to its central location and high foot traffic, with 15 coffee shops and high competition levels.",
            fallback: true,
          });
        }

        if (userMessage.includes("investment") || userMessage.includes("opportunity")) {
          return NextResponse.json({
            message: "Top investment opportunities: 1) **Songinokhairkhan** - High opportunity, low competition, 85% growth potential, ₮19,000/sqm rent. 2) **Khan-Uul** - Medium opportunity, diplomatic area, 75% growth potential, ₮32,000/sqm rent. 3) **Bayanzurkh** - Large population, 70% growth potential, ₮26,500/sqm rent.",
            fallback: true,
          });
        }
      }
    } catch (fallbackError) {
      // If fallback fails, continue with error response
      console.warn('Fallback response failed:', fallbackError);
    }

    return NextResponse.json(
      {
        error: errorMessage,
        timestamp: Date.now(),
      },
      { status: statusCode }
    );
  }
}
