"use client";

import { <PERSON><PERSON>in, <PERSON><PERSON>ding<PERSON>p, Alert<PERSON><PERSON>gle, CheckCircle, DollarSign, Users } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>hart, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ZAxis } from "recharts";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

import {
  footTrafficData,
  trafficChartConfig,
  investmentOpportunities,
  districtAnalysisData,
  districtChartConfig,
} from "./market-data";

export function LocationInsights() {
  return (
    <div className="grid grid-cols-1 gap-4 *:data-[slot=card]:shadow-xs lg:grid-cols-2">
      {/* Foot Traffic Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Foot Traffic Analysis</CardTitle>
          <CardDescription>High-traffic locations and existing coffee shop density</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={trafficChartConfig} className="h-[300px]">
            <BarChart data={footTrafficData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="location" 
                tick={{ fontSize: 10 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <ChartTooltip 
                content={<ChartTooltipContent />}
                formatter={(value, name) => [
                  name === "score" ? `${value}/100` : `${value} shops`,
                  name === "score" ? "Traffic Score" : "Coffee Shops"
                ]}
              />
              <Bar yAxisId="left" dataKey="score" fill="var(--chart-1)" radius={[4, 4, 0, 0]} />
              <Bar yAxisId="right" dataKey="shops" fill="var(--chart-2)" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Rental Price vs Density */}
      <Card>
        <CardHeader>
          <CardTitle>Rent vs Competition</CardTitle>
          <CardDescription>Average rental prices compared to coffee shop density by district</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={districtChartConfig} className="h-[300px]">
            <ScatterChart data={districtAnalysisData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="avgRent" 
                type="number" 
                domain={['dataMin - 5000', 'dataMax + 5000']}
                tickFormatter={(value) => `₮${(value / 1000).toFixed(0)}k`}
              />
              <YAxis 
                dataKey="coffeeShops" 
                type="number"
                domain={[0, 'dataMax + 2']}
              />
              <ZAxis dataKey="population" range={[50, 200]} />
              <ChartTooltip 
                content={<ChartTooltipContent />}
                formatter={(value, name, props) => [
                  name === "avgRent" ? `₮${Number(value).toLocaleString()}` :
                  name === "coffeeShops" ? `${value} shops` :
                  `${Number(value).toLocaleString()}`,
                  name === "avgRent" ? "Avg Rent" :
                  name === "coffeeShops" ? "Coffee Shops" : "Population"
                ]}
                labelFormatter={(label, payload) => 
                  payload?.[0]?.payload?.district || "District"
                }
              />
              <Scatter dataKey="coffeeShops" fill="var(--chart-1)" />
            </ScatterChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Investment Opportunities */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>Investment Opportunities</CardTitle>
          <CardDescription>Recommended districts for new coffee shop investments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {investmentOpportunities.map((opportunity, index) => (
              <div key={opportunity.district} className="rounded-lg border p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    {opportunity.district}
                  </h4>
                  <Badge 
                    variant={
                      opportunity.opportunity === "High" ? "default" :
                      opportunity.opportunity === "Medium" ? "secondary" : "outline"
                    }
                  >
                    {opportunity.opportunity} Opportunity
                  </Badge>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Growth Potential</span>
                    <span className="font-medium">{opportunity.growthPotential}%</span>
                  </div>
                  <Progress value={opportunity.growthPotential} className="h-2" />
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      Avg Rent
                    </span>
                    <span className="font-medium">₮{opportunity.avgRent.toLocaleString()}/sqm</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Competition</span>
                    <Badge 
                      variant={
                        opportunity.competition === "Low" ? "default" :
                        opportunity.competition === "Medium" ? "secondary" : "destructive"
                      }
                      className="text-xs"
                    >
                      {opportunity.competition}
                    </Badge>
                  </div>
                  
                  <p className="text-xs text-muted-foreground mt-2">
                    {opportunity.reason}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Key Insights */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>Key Market Insights</CardTitle>
          <CardDescription>Important findings for coffee shop investment decisions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <h5 className="font-medium text-sm">Prime Locations</h5>
                <p className="text-xs text-muted-foreground">
                  Government Square and Central Tower areas show highest foot traffic
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h5 className="font-medium text-sm">Growth Areas</h5>
                <p className="text-xs text-muted-foreground">
                  Songinokhairkhan district shows 85% growth potential with low competition
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <DollarSign className="h-5 w-5 text-yellow-500 mt-0.5" />
              <div>
                <h5 className="font-medium text-sm">Cost Efficiency</h5>
                <p className="text-xs text-muted-foreground">
                  Outer districts offer 60% lower rent with growing populations
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-500 mt-0.5" />
              <div>
                <h5 className="font-medium text-sm">Market Saturation</h5>
                <p className="text-xs text-muted-foreground">
                  Central districts approaching saturation with 15+ shops per district
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
