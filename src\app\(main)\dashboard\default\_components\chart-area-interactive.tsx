"use client";

import * as React from "react";

import { Area, AreaChart, CartesianGrid, XAxis } from "recharts";

import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useIsMobile } from "@/hooks/use-mobile";

export const description = "Coffee market trends and consumption analytics";

// Coffee market data: Global consumption vs Mongolia consumption (in thousands of cups per day)
const chartData = [
  { date: "2024-04-01", global: 2850, mongolia: 45 },
  { date: "2024-04-02", global: 2920, mongolia: 48 },
  { date: "2024-04-03", global: 2780, mongolia: 42 },
  { date: "2024-04-04", global: 3100, mongolia: 52 },
  { date: "2024-04-05", global: 3250, mongolia: 58 },
  { date: "2024-04-06", global: 3180, mongolia: 55 },
  { date: "2024-04-07", global: 2950, mongolia: 47 },
  { date: "2024-04-08", global: 3350, mongolia: 62 },
  { date: "2024-04-09", global: 2650, mongolia: 38 },
  { date: "2024-04-10", global: 2980, mongolia: 49 },
  { date: "2024-04-11", global: 3200, mongolia: 56 },
  { date: "2024-04-12", global: 3050, mongolia: 51 },
  { date: "2024-04-13", global: 3280, mongolia: 59 },
  { date: "2024-04-14", global: 2850, mongolia: 44 },
  { date: "2024-04-15", global: 2750, mongolia: 41 },
  { date: "2024-04-16", global: 2820, mongolia: 43 },
  { date: "2024-04-17", global: 3450, mongolia: 65 },
  { date: "2024-04-18", global: 3380, mongolia: 63 },
  { date: "2024-04-19", global: 3020, mongolia: 50 },
  { date: "2024-04-20", global: 2680, mongolia: 39 },
  { date: "2024-04-21", global: 2850, mongolia: 45 },
  { date: "2024-04-22", global: 2920, mongolia: 47 },
  { date: "2024-04-23", global: 2880, mongolia: 46 },
  { date: "2024-04-24", global: 3320, mongolia: 60 },
  { date: "2024-04-25", global: 3150, mongolia: 54 },
  { date: "2024-04-26", global: 2720, mongolia: 40 },
  { date: "2024-04-27", global: 3420, mongolia: 64 },
  { date: "2024-04-28", global: 2890, mongolia: 48 },
  { date: "2024-04-29", global: 3180, mongolia: 55 },
  { date: "2024-04-30", global: 3480, mongolia: 67 },
  { date: "2024-05-01", global: 2850, mongolia: 46 },
  { date: "2024-05-02", global: 3120, mongolia: 53 },
  { date: "2024-05-03", global: 2980, mongolia: 49 },
  { date: "2024-05-04", global: 3380, mongolia: 63 },
  { date: "2024-05-05", global: 3520, mongolia: 68 },
  { date: "2024-05-06", global: 3580, mongolia: 71 },
  { date: "2024-05-07", global: 3250, mongolia: 58 },
  { date: "2024-05-08", global: 2890, mongolia: 47 },
  { date: "2024-05-09", global: 2950, mongolia: 48 },
  { date: "2024-05-10", global: 3180, mongolia: 55 },
  { date: "2024-05-11", global: 3280, mongolia: 59 },
  { date: "2024-05-12", global: 3050, mongolia: 51 },
  { date: "2024-05-13", global: 2920, mongolia: 47 },
  { date: "2024-05-14", global: 3480, mongolia: 66 },
  { date: "2024-05-15", global: 3420, mongolia: 64 },
  { date: "2024-05-16", global: 3320, mongolia: 60 },
  { date: "2024-05-17", global: 3550, mongolia: 69 },
  { date: "2024-05-18", global: 3220, mongolia: 57 },
  { date: "2024-05-19", global: 2980, mongolia: 49 },
  { date: "2024-05-20", global: 2850, mongolia: 45 },
  { date: "2024-05-21", global: 2720, mongolia: 41 },
  { date: "2024-05-22", global: 2680, mongolia: 40 },
  { date: "2024-05-23", global: 3080, mongolia: 52 },
  { date: "2024-05-24", global: 3150, mongolia: 54 },
  { date: "2024-05-25", global: 2980, mongolia: 50 },
  { date: "2024-05-26", global: 2920, mongolia: 48 },
  { date: "2024-05-27", global: 3420, mongolia: 64 },
  { date: "2024-05-28", global: 3020, mongolia: 51 },
  { date: "2024-05-29", global: 2750, mongolia: 42 },
  { date: "2024-05-30", global: 3280, mongolia: 58 },
  { date: "2024-05-31", global: 2950, mongolia: 48 },
  { date: "2024-06-01", global: 2920, mongolia: 47 },
  { date: "2024-06-02", global: 3450, mongolia: 65 },
  { date: "2024-06-03", global: 2820, mongolia: 44 },
  { date: "2024-06-04", global: 3380, mongolia: 62 },
  { date: "2024-06-05", global: 2750, mongolia: 42 },
  { date: "2024-06-06", global: 3150, mongolia: 54 },
  { date: "2024-06-07", global: 3250, mongolia: 58 },
  { date: "2024-06-08", global: 3320, mongolia: 60 },
  { date: "2024-06-09", global: 3480, mongolia: 66 },
  { date: "2024-06-10", global: 2880, mongolia: 46 },
  { date: "2024-06-11", global: 2780, mongolia: 43 },
  { date: "2024-06-12", global: 3520, mongolia: 68 },
  { date: "2024-06-13", global: 2720, mongolia: 41 },
  { date: "2024-06-14", global: 3420, mongolia: 64 },
  { date: "2024-06-15", global: 3180, mongolia: 56 },
  { date: "2024-06-16", global: 3280, mongolia: 59 },
  { date: "2024-06-17", global: 3580, mongolia: 71 },
  { date: "2024-06-18", global: 2850, mongolia: 45 },
  { date: "2024-06-19", global: 3220, mongolia: 57 },
  { date: "2024-06-20", global: 3450, mongolia: 65 },
  { date: "2024-06-21", global: 2920, mongolia: 48 },
  { date: "2024-06-22", global: 3150, mongolia: 54 },
  { date: "2024-06-23", global: 3550, mongolia: 70 },
  { date: "2024-06-24", global: 2880, mongolia: 46 },
  { date: "2024-06-25", global: 2920, mongolia: 47 },
  { date: "2024-06-26", global: 3380, mongolia: 62 },
  { date: "2024-06-27", global: 3480, mongolia: 66 },
  { date: "2024-06-28", global: 2890, mongolia: 47 },
  { date: "2024-06-29", global: 2820, mongolia: 44 },
  { date: "2024-06-30", global: 3420, mongolia: 64 },
];

const chartConfig = {
  consumption: {
    label: "Coffee Consumption",
  },
  global: {
    label: "Global (thousands)",
    color: "var(--chart-1)",
  },
  mongolia: {
    label: "Mongolia (thousands)",
    color: "var(--chart-2)",
  },
} satisfies ChartConfig;

export function ChartAreaInteractive() {
  const isMobile = useIsMobile();
  const [timeRange, setTimeRange] = React.useState("90d");

  React.useEffect(() => {
    if (isMobile) {
      setTimeRange("7d");
    }
  }, [isMobile]);

  const filteredData = chartData.filter((item) => {
    const date = new Date(item.date);
    const referenceDate = new Date("2024-06-30");
    let daysToSubtract = 90;
    if (timeRange === "30d") {
      daysToSubtract = 30;
    } else if (timeRange === "7d") {
      daysToSubtract = 7;
    }
    const startDate = new Date(referenceDate);
    startDate.setDate(startDate.getDate() - daysToSubtract);
    return date >= startDate;
  });

  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>Coffee Market Trends Analytics</CardTitle>
        <CardDescription>
          <span className="hidden @[540px]/card:block">Global and Mongolia coffee consumption patterns and market growth analysis</span>
          <span className="@[540px]/card:hidden">Coffee market trends</span>
        </CardDescription>
        <CardAction>
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={setTimeRange}
            variant="outline"
            className="hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex"
          >
            <ToggleGroupItem value="90d">Last 3 months</ToggleGroupItem>
            <ToggleGroupItem value="30d">Last 30 days</ToggleGroupItem>
            <ToggleGroupItem value="7d">Last 7 days</ToggleGroupItem>
          </ToggleGroup>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger
              className="flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden"
              size="sm"
              aria-label="Select a value"
            >
              <SelectValue placeholder="Last 3 months" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="90d" className="rounded-lg">
                Last 3 months
              </SelectItem>
              <SelectItem value="30d" className="rounded-lg">
                Last 30 days
              </SelectItem>
              <SelectItem value="7d" className="rounded-lg">
                Last 7 days
              </SelectItem>
            </SelectContent>
          </Select>
        </CardAction>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
          <AreaChart data={filteredData}>
            <defs>
              <linearGradient id="fillGlobal" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="var(--color-global)" stopOpacity={1.0} />
                <stop offset="95%" stopColor="var(--color-global)" stopOpacity={0.1} />
              </linearGradient>
              <linearGradient id="fillMongolia" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="var(--color-mongolia)" stopOpacity={0.8} />
                <stop offset="95%" stopColor="var(--color-mongolia)" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <ChartTooltip
              cursor={false}
              defaultIndex={isMobile ? -1 : 10}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    });
                  }}
                  indicator="dot"
                />
              }
            />
            <Area dataKey="mongolia" type="natural" fill="url(#fillMongolia)" stroke="var(--color-mongolia)" stackId="a" />
            <Area dataKey="global" type="natural" fill="url(#fillGlobal)" stroke="var(--color-global)" stackId="a" />
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
