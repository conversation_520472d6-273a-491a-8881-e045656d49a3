"use client";

import { TrendingUp, TrendingDown, Target } from "lucide-react";
import { <PERSON><PERSON>hart, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid } from "recharts";

import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { formatCurrency } from "@/lib/utils";

import { marketInvestmentData, marketInvestmentConfig } from "./mongolia-market-data";

export function FinancialOverview() {
  const totalInvestments = marketInvestmentData.reduce((acc, item) => acc + item.investments, 0);
  const totalReturns = marketInvestmentData.reduce((acc, item) => acc + item.returns, 0);
  const totalExpenses = marketInvestmentData.reduce((acc, item) => acc + item.expenses, 0);
  const netProfit = totalReturns - totalInvestments - totalExpenses;
  const roi = ((netProfit / totalInvestments) * 100).toFixed(1);

  return (
    <Card className="shadow-xs">
      <CardHeader>
        <CardTitle>Market Investment Overview</CardTitle>
        <CardDescription>Track market investments, returns, and operational costs for Mongolia business opportunities.</CardDescription>
        <CardAction>
          <Select defaultValue="last-year">
            <SelectTrigger>
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last-year">Last Year</SelectItem>
              <SelectItem value="last-quarter">Last Quarter</SelectItem>
              <SelectItem value="ytd">Year to Date</SelectItem>
            </SelectContent>
          </Select>
        </CardAction>
      </CardHeader>
      <CardContent>
        <Separator />
        <div className="flex flex-col items-start justify-between gap-2 py-5 md:flex-row md:items-stretch md:gap-0">
          <div className="flex flex-1 items-center justify-center gap-2">
            <div className="flex size-10 shrink-0 items-center justify-center rounded-full border">
              <TrendingUp className="stroke-chart-1 size-6" />
            </div>
            <div>
              <p className="text-muted-foreground text-xs uppercase">Total Investments</p>
              <p className="font-medium tabular-nums">₮{(totalInvestments / 1000000).toFixed(0)}M</p>
            </div>
          </div>
          <Separator orientation="vertical" className="!h-auto" />
          <div className="flex flex-1 items-center justify-center gap-2">
            <div className="flex size-10 shrink-0 items-center justify-center rounded-full border">
              <TrendingDown className="stroke-chart-2 size-6" />
            </div>
            <div>
              <p className="text-muted-foreground text-xs uppercase">Returns</p>
              <p className="font-medium tabular-nums">₮{(totalReturns / 1000000).toFixed(0)}M</p>
            </div>
          </div>
          <Separator orientation="vertical" className="!h-auto" />
          <div className="flex flex-1 items-center justify-center gap-2">
            <div className="flex size-10 shrink-0 items-center justify-center rounded-full border">
              <Target className="stroke-chart-3 size-6" />
            </div>
            <div>
              <p className="text-muted-foreground text-xs uppercase">ROI</p>
              <p className="font-medium tabular-nums">{((netProfit / totalInvestments) * 100).toFixed(1)}%</p>
            </div>
          </div>
        </div>
        <Separator />
        <ChartContainer className="max-h-72 w-full" config={marketInvestmentConfig}>
          <BarChart margin={{ left: -25, right: 0, top: 25, bottom: 0 }} accessibilityLayer data={marketInvestmentData}>
            <CartesianGrid vertical={false} />
            <XAxis dataKey="month" tickLine={false} tickMargin={10} axisLine={false} />
            <YAxis
              axisLine={false}
              tickLine={false}
              tickMargin={8}
              tickFormatter={(value) => `₮${value >= 1000000 ? (value / 1000000).toFixed(0) + "M" : (value / 1000).toFixed(0) + "K"}`}
              domain={[0, 100000000]}
            />
            <ChartTooltip
              content={<ChartTooltipContent hideLabel />}
              formatter={(value, name) => [
                `₮${(Number(value) / 1000000).toFixed(1)}M`,
                name === "investments" ? "Investments" :
                name === "returns" ? "Returns" : "Expenses"
              ]}
            />
            <Bar dataKey="investments" stackId="a" fill={marketInvestmentConfig.investments.color} />
            <Bar dataKey="expenses" stackId="a" fill={marketInvestmentConfig.expenses.color} />
            <Bar dataKey="returns" stackId="a" fill={marketInvestmentConfig.returns.color} />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
