import { AccountOverview } from "./_components/account-overview";
import { CurrencyExchange } from "./_components/currency-exchange";
import { ExpenseSummary } from "./_components/expense-summary";
import { FinancialOverview } from "./_components/financial-overview";

export default function Page() {
  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      {/* Header Section */}
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Financial Management Dashboard</h1>
        <p className="text-muted-foreground">
          Investment portfolio management, market analysis, and financial performance tracking for Mongolia business opportunities
        </p>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {/* Investment Portfolio & Account Management */}
        <div className="flex flex-col gap-4 lg:col-span-1">
          <AccountOverview />
        </div>

        {/* Financial Analytics & Market Data */}
        <div className="flex flex-col gap-4 lg:col-span-2">
          <div className="flex-1">
            <FinancialOverview />
          </div>
          <div className="grid flex-1 grid-cols-1 gap-4 *:data-[slot=card]:shadow-xs md:grid-cols-2">
            <ExpenseSummary />
            <CurrencyExchange />
          </div>
        </div>
      </div>
    </div>
  );
}
