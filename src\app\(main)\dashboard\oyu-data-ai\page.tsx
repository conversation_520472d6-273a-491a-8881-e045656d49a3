import { OverviewCards } from "./_components/overview-cards";
import { MarketAnalysisCharts } from "./_components/market-analysis-charts";
import { CoffeeShopTable } from "./_components/coffee-shop-table";
import { LocationInsights } from "./_components/location-insights";
import coffeeShopsData from "./_components/coffee-shops-data.json";

export default function Page() {
  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      {/* Header Section */}
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Oyu-Data-AI Dashboard</h1>
        <p className="text-muted-foreground">
          Coffee Shop Market Intelligence for Ulaanbaatar - Comprehensive data insights for smarter business decisions
        </p>
      </div>

      {/* Overview Cards */}
      <OverviewCards />

      {/* Market Analysis Charts */}
      <MarketAnalysisCharts />

      {/* Location Insights */}
      <LocationInsights />

      {/* Coffee Shop Data Table */}
      <CoffeeShopTable data={coffeeShopsData} />
    </div>
  );
}
