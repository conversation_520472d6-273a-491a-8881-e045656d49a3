"use client";

import * as React from "react";

import { Plus } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useDataTableInstance } from "@/hooks/use-data-table-instance";

import { DataTable } from "../../../../../components/data-table/data-table";
import { DataTablePagination } from "../../../../../components/data-table/data-table-pagination";
import { DataTableViewOptions } from "../../../../../components/data-table/data-table-view-options";
import { withDndColumn } from "../../../../../components/data-table/table-utils";

import { coffeeSupplierColumns, coffeeEquipmentColumns, menuItemColumns } from "./columns.coffee-supply";
import { coffeeBeanSuppliers, coffeeEquipment, menuItems } from "./coffee-supply-chain.config";

export function TableCards() {
  // Supplier table setup
  const [supplierData, setSupplierData] = React.useState(() => coffeeBeanSuppliers);
  const supplierColumns = withDndColumn(coffeeSupplierColumns);
  const supplierTable = useDataTableInstance({ 
    data: supplierData, 
    columns: supplierColumns, 
    getRowId: (row) => row.id.toString() 
  });

  // Equipment table setup
  const [equipmentData, setEquipmentData] = React.useState(() => coffeeEquipment);
  const equipmentColumns = withDndColumn(coffeeEquipmentColumns);
  const equipmentTable = useDataTableInstance({ 
    data: equipmentData, 
    columns: equipmentColumns, 
    getRowId: (row) => row.id.toString() 
  });

  // Menu items table setup
  const [menuData, setMenuData] = React.useState(() => menuItems);
  const menuColumns = withDndColumn(menuItemColumns);
  const menuTable = useDataTableInstance({ 
    data: menuData, 
    columns: menuColumns, 
    getRowId: (row) => row.id.toString() 
  });

  return (
    <Card className="shadow-xs">
      <CardHeader>
        <CardTitle>Coffee Supply Chain Management</CardTitle>
        <CardDescription>
          Comprehensive management of suppliers, equipment, and menu items for coffee shop operations
        </CardDescription>
        <CardAction>
          <Select defaultValue="all">
            <SelectTrigger>
              <SelectValue placeholder="Filter data" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Data</SelectItem>
              <SelectItem value="active">Active Only</SelectItem>
              <SelectItem value="recent">Recent Updates</SelectItem>
            </SelectContent>
          </Select>
        </CardAction>
      </CardHeader>
      <CardContent>
        <Separator />
        <div className="pt-4">
        {/* Supply Chain Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 *:data-[slot=card]:shadow-xs">
          <Card className="@container/card">
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">Active Suppliers</div>
              <div className="text-2xl font-bold tabular-nums">{supplierData.length}</div>
            </CardContent>
          </Card>
          <Card className="@container/card">
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">Equipment Items</div>
              <div className="text-2xl font-bold tabular-nums">{equipmentData.length}</div>
            </CardContent>
          </Card>
          <Card className="@container/card">
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">Menu Items</div>
              <div className="text-2xl font-bold tabular-nums">{menuData.length}</div>
            </CardContent>
          </Card>
          <Card className="@container/card">
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">Avg Reliability</div>
              <div className="text-2xl font-bold tabular-nums text-green-600">
                {(supplierData.reduce((sum, s) => sum + s.reliability, 0) / supplierData.length).toFixed(1)}%
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="suppliers" className="w-full">
          <div className="flex items-center justify-between">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="suppliers">Coffee Suppliers</TabsTrigger>
              <TabsTrigger value="equipment">Equipment & Setup</TabsTrigger>
              <TabsTrigger value="menu">Menu & Pricing</TabsTrigger>
            </TabsList>
            <div className="ml-auto flex items-center gap-2">
              <DataTableViewOptions table={supplierTable} />
              <Button variant="outline" size="sm">
                <Plus />
                <span className="hidden lg:inline">Add New</span>
              </Button>
            </div>
          </div>

          <TabsContent value="suppliers" className="flex flex-col gap-4">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Coffee Bean Suppliers</h3>
              <p className="text-sm text-muted-foreground">
                Manage relationships with coffee bean suppliers, track pricing, quality grades, and delivery schedules
              </p>
            </div>
            <div className="overflow-hidden rounded-lg border">
              <DataTable 
                dndEnabled 
                table={supplierTable} 
                columns={supplierColumns} 
                onReorder={setSupplierData} 
              />
            </div>
            <DataTablePagination table={supplierTable} />
          </TabsContent>

          <TabsContent value="equipment" className="flex flex-col gap-4">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Coffee Shop Equipment & Setup</h3>
              <p className="text-sm text-muted-foreground">
                Track equipment suppliers, pricing, specifications, and delivery times for coffee shop setup
              </p>
            </div>
            <div className="overflow-hidden rounded-lg border">
              <DataTable 
                dndEnabled 
                table={equipmentTable} 
                columns={equipmentColumns} 
                onReorder={setEquipmentData} 
              />
            </div>
            <DataTablePagination table={equipmentTable} />
          </TabsContent>

          <TabsContent value="menu" className="flex flex-col gap-4">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Menu Items & Pricing Strategy</h3>
              <p className="text-sm text-muted-foreground">
                Analyze menu item profitability, pricing strategies, and popularity metrics for optimal revenue
              </p>
            </div>
            <div className="overflow-hidden rounded-lg border">
              <DataTable 
                dndEnabled 
                table={menuTable} 
                columns={menuColumns} 
                onReorder={setMenuData} 
              />
            </div>
            <DataTablePagination table={menuTable} />
          </TabsContent>
        </Tabs>
        </div>
      </CardContent>
    </Card>
  );
}
