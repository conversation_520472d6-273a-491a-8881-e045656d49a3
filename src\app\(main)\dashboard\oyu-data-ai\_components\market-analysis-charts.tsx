"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, XAxis, YAxis, CartesianGrid, Pie, PieChart, Cell, Line, LineChart, Area, AreaChart } from "recharts";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";

import {
  districtAnalysisData,
  districtChartConfig,
  priceDistributionData,
  priceChartConfig,
  monthlyTrendsData,
  trendsChartConfig,
  competitionData,
  competitionChartConfig,
} from "./market-data";

const COLORS = ["var(--chart-1)", "var(--chart-2)", "var(--chart-3)", "var(--chart-4)", "var(--chart-5)"];

export function MarketAnalysisCharts() {
  return (
    <div className="grid grid-cols-1 gap-4 *:data-[slot=card]:shadow-xs lg:grid-cols-2">
      {/* District Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Coffee Shops by District</CardTitle>
          <CardDescription>Distribution and density across Ulaanbaatar districts</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <ChartContainer config={districtChartConfig} className="h-[320px] w-full">
            <BarChart
              data={districtAnalysisData}
              margin={{ top: 20, right: 30, left: 40, bottom: 80 }}
              width="100%"
              height="100%"
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="district"
                tick={{ fontSize: 10 }}
                angle={-45}
                textAnchor="end"
                height={80}
                interval={0}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <ChartTooltip
                content={<ChartTooltipContent />}
                formatter={(value, name) => [
                  name === "coffeeShops" ? value :
                  name === "density" ? `${(Number(value) * 1000).toFixed(1)} per 1000` :
                  `₮${Number(value).toLocaleString()}`,
                  name === "coffeeShops" ? "Coffee Shops" :
                  name === "density" ? "Density" : "Avg Rent"
                ]}
              />
              <Bar dataKey="coffeeShops" fill="var(--chart-1)" radius={[4, 4, 0, 0]} name="Coffee Shops" />
              <Bar dataKey="density" fill="var(--chart-2)" radius={[4, 4, 0, 0]} name="Density" />
              <Bar dataKey="avgRent" fill="var(--chart-3)" radius={[4, 4, 0, 0]} name="Avg Rent" />
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Price Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Price Distribution</CardTitle>
          <CardDescription>Coffee price ranges across all shops</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <ChartContainer config={priceChartConfig} className="h-[320px] w-full">
            <PieChart width="100%" height="100%">
              <Pie
                data={priceDistributionData}
                cx="50%"
                cy="50%"
                outerRadius={90}
                dataKey="count"
                label={({ priceRange, percentage }) => `${priceRange} (${percentage}%)`}
                labelLine={false}
              >
                {priceDistributionData.map((entry, index) => (
                  <Cell key={`price-${entry.priceRange}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <ChartTooltip
                content={<ChartTooltipContent />}
                formatter={(value, name) => [`${value} shops`, "Count"]}
              />
            </PieChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Monthly Trends */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>Market Trends</CardTitle>
          <CardDescription>Monthly revenue, customer count, and new shop openings</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <ChartContainer config={trendsChartConfig} className="h-[400px] w-full">
            <LineChart
              data={monthlyTrendsData}
              margin={{ top: 20, right: 60, left: 60, bottom: 20 }}
              width="100%"
              height="100%"
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="month"
                tick={{ fontSize: 12 }}
                tickMargin={10}
              />
              <YAxis
                yAxisId="left"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `₮${(value / 1000000).toFixed(1)}M`}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                tick={{ fontSize: 12 }}
              />
              <ChartTooltip
                content={<ChartTooltipContent />}
                formatter={(value, name) => [
                  name === "revenue" ? `₮${Number(value).toLocaleString()}` :
                  name === "customers" ? `${Number(value).toLocaleString()}` :
                  `${value}`,
                  name === "revenue" ? "Revenue" :
                  name === "customers" ? "Customers" : "New Shops"
                ]}
              />
              <ChartLegend content={<ChartLegendContent />} />
              <Area
                yAxisId="left"
                type="monotone"
                dataKey="revenue"
                stroke="var(--chart-1)"
                fill="var(--chart-1)"
                fillOpacity={0.3}
                strokeWidth={2}
              />
              <Line
                yAxisId="left"
                type="monotone"
                dataKey="customers"
                stroke="var(--chart-2)"
                strokeWidth={2}
                dot={{ r: 4 }}
              />
              <Bar
                yAxisId="right"
                dataKey="newShops"
                fill="var(--chart-3)"
                radius={[4, 4, 0, 0]}
                maxBarSize={30}
              />
            </LineChart>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Competition Analysis */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle>Market Trends and Competition Analysis</CardTitle>
          <CardDescription>Market share and pricing by business type</CardDescription>
        </CardHeader>
        <CardContent className="p-4">
          <ChartContainer config={competitionChartConfig} className="h-[350px] w-full">
            <BarChart
              data={competitionData}
              margin={{ top: 20, right: 80, left: 60, bottom: 40 }}
              width="100%"
              height="100%"
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="type"
                tick={{ fontSize: 12 }}
                tickMargin={10}
                angle={-15}
                textAnchor="end"
                height={60}
              />
              <YAxis
                yAxisId="left"
                tick={{ fontSize: 12 }}
                label={{ value: 'Count / Share (%)', angle: -90, position: 'insideLeft' }}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `₮${(value / 1000).toFixed(0)}k`}
                label={{ value: 'Avg Price (₮)', angle: 90, position: 'insideRight' }}
              />
              <ChartTooltip
                content={<ChartTooltipContent />}
                formatter={(value, name) => [
                  name === "count" ? `${value} shops` :
                  name === "marketShare" ? `${value}%` :
                  `₮${Number(value).toLocaleString()}`,
                  name === "count" ? "Count" :
                  name === "marketShare" ? "Market Share" : "Avg Price"
                ]}
              />
              <ChartLegend content={<ChartLegendContent />} />
              <Bar yAxisId="left" dataKey="count" fill="var(--chart-1)" radius={[4, 4, 0, 0]} name="Shop Count" />
              <Bar yAxisId="left" dataKey="marketShare" fill="var(--chart-2)" radius={[4, 4, 0, 0]} name="Market Share %" />
              <Bar yAxisId="right" dataKey="avgPrice" fill="var(--chart-3)" radius={[4, 4, 0, 0]} name="Avg Price" />
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
}
