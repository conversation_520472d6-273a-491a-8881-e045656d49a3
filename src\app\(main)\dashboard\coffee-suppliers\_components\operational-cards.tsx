"use client";

import { Package, TrendingUp, BarChart3 } from "lucide-react";
import { <PERSON>, Bar<PERSON>hart, XAxis, YAxis, CartesianGrid, Line, LineChart, Area, AreaChart } from "recharts";

import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartConfig } from "@/components/ui/chart";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

import { coffeeBeanSuppliers } from "./coffee-supply-chain.config";



// Chart data for supplier performance metrics
const supplierPerformanceData = coffeeBeanSuppliers.map(supplier => ({
  name: supplier.name.split(' ')[0], // Use first word for shorter labels
  reliability: supplier.reliability,
  volume: supplier.monthlyVolume,
  price: supplier.pricePerKg / 1000, // Convert to thousands for better display
}));

// Chart data for pricing trends (simulated monthly data)
const pricingTrendsData = [
  { month: 'Oct', ethiopian: 17.8, colombian: 16.2, brazilian: 13.8, guatemalan: 18.5 },
  { month: 'Nov', ethiopian: 18.1, colombian: 16.5, brazilian: 14.0, guatemalan: 18.8 },
  { month: 'Dec', ethiopian: 18.3, colombian: 16.7, brazilian: 14.1, guatemalan: 19.0 },
  { month: 'Jan', ethiopian: 18.5, colombian: 16.8, brazilian: 14.2, guatemalan: 19.2 },
];

// Chart data for supply volume trends
const supplyVolumeData = [
  { month: 'Oct', total: 11800, delivered: 11200, pending: 600 },
  { month: 'Nov', total: 12200, delivered: 11800, pending: 400 },
  { month: 'Dec', total: 12800, delivered: 12300, pending: 500 },
  { month: 'Jan', total: 13300, delivered: 12800, pending: 500 },
];

// Chart configurations
const performanceChartConfig: ChartConfig = {
  reliability: {
    label: "Reliability %",
    color: "hsl(var(--chart-1))",
  },
  volume: {
    label: "Volume (kg)",
    color: "hsl(var(--chart-2))",
  },
};

const pricingChartConfig: ChartConfig = {
  ethiopian: {
    label: "Ethiopian",
    color: "hsl(var(--chart-1))",
  },
  colombian: {
    label: "Colombian",
    color: "hsl(var(--chart-2))",
  },
  brazilian: {
    label: "Brazilian",
    color: "hsl(var(--chart-3))",
  },
  guatemalan: {
    label: "Guatemalan",
    color: "hsl(var(--chart-4))",
  },
};

const volumeChartConfig: ChartConfig = {
  delivered: {
    label: "Delivered",
    color: "hsl(var(--chart-1))",
  },
  pending: {
    label: "Pending",
    color: "hsl(var(--chart-2))",
  },
};

export function OperationalCards() {
  return (
    <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
      {/* Supplier Performance Metrics Chart */}
      <Card className="shadow-xs">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Supplier Performance Metrics
          </CardTitle>
          <CardDescription>
            Reliability scores and monthly volume capacity by supplier
          </CardDescription>
          <CardAction>
            <Select defaultValue="reliability">
              <SelectTrigger>
                <SelectValue placeholder="Select metric" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="reliability">Reliability</SelectItem>
                <SelectItem value="volume">Volume</SelectItem>
                <SelectItem value="both">Both Metrics</SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent>
          <Separator />
          <div className="pt-4">
            <ChartContainer config={performanceChartConfig} className="h-[300px]">
              <BarChart data={supplierPerformanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  tick={{ fontSize: 12 }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis yAxisId="left" orientation="left" tick={{ fontSize: 12 }} />
                <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 12 }} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  animationDuration={300}
                />
                <Bar
                  yAxisId="left"
                  dataKey="reliability"
                  fill="var(--color-reliability)"
                  radius={[4, 4, 0, 0]}
                  animationDuration={1000}
                  animationBegin={0}
                />
                <Bar
                  yAxisId="right"
                  dataKey="volume"
                  fill="var(--color-volume)"
                  radius={[4, 4, 0, 0]}
                  animationDuration={1000}
                  animationBegin={200}
                />
              </BarChart>
            </ChartContainer>
          </div>
        </CardContent>
      </Card>

      {/* Coffee Bean Pricing Trends Chart */}
      <Card className="shadow-xs">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Coffee Bean Pricing Trends
          </CardTitle>
          <CardDescription>
            Monthly price movements for different coffee bean origins (₮ thousands/kg)
          </CardDescription>
          <CardAction>
            <Select defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Select origin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Origins</SelectItem>
                <SelectItem value="ethiopian">Ethiopian</SelectItem>
                <SelectItem value="colombian">Colombian</SelectItem>
                <SelectItem value="brazilian">Brazilian</SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent>
          <Separator />
          <div className="pt-4">
            <ChartContainer config={pricingChartConfig} className="h-[300px]">
              <LineChart data={pricingTrendsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="month"
                  tick={{ fontSize: 12 }}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  animationDuration={300}
                />
                <Line
                  type="monotone"
                  dataKey="ethiopian"
                  stroke="var(--color-ethiopian)"
                  strokeWidth={3}
                  dot={{ r: 4 }}
                  animationDuration={1500}
                  animationBegin={0}
                />
                <Line
                  type="monotone"
                  dataKey="colombian"
                  stroke="var(--color-colombian)"
                  strokeWidth={3}
                  dot={{ r: 4 }}
                  animationDuration={1500}
                  animationBegin={300}
                />
                <Line
                  type="monotone"
                  dataKey="brazilian"
                  stroke="var(--color-brazilian)"
                  strokeWidth={3}
                  dot={{ r: 4 }}
                  animationDuration={1500}
                  animationBegin={600}
                />
                <Line
                  type="monotone"
                  dataKey="guatemalan"
                  stroke="var(--color-guatemalan)"
                  strokeWidth={3}
                  dot={{ r: 4 }}
                  animationDuration={1500}
                  animationBegin={900}
                />
              </LineChart>
            </ChartContainer>
          </div>
        </CardContent>
      </Card>

      {/* Supply Volume Trends Chart */}
      <Card className="shadow-xs lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Monthly Supply Volume Trends
          </CardTitle>
          <CardDescription>
            Total supply volumes, delivered quantities, and pending orders (kg)
          </CardDescription>
          <CardAction>
            <Select defaultValue="stacked">
              <SelectTrigger>
                <SelectValue placeholder="Chart type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="stacked">Stacked View</SelectItem>
                <SelectItem value="separate">Separate Lines</SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent>
          <Separator />
          <div className="pt-4">
            <ChartContainer config={volumeChartConfig} className="h-[300px]">
              <AreaChart data={supplyVolumeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="month"
                  tick={{ fontSize: 12 }}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  animationDuration={300}
                />
                <Area
                  type="monotone"
                  dataKey="delivered"
                  stackId="1"
                  stroke="var(--color-delivered)"
                  fill="var(--color-delivered)"
                  fillOpacity={0.6}
                  animationDuration={1200}
                  animationBegin={0}
                />
                <Area
                  type="monotone"
                  dataKey="pending"
                  stackId="1"
                  stroke="var(--color-pending)"
                  fill="var(--color-pending)"
                  fillOpacity={0.6}
                  animationDuration={1200}
                  animationBegin={400}
                />
              </AreaChart>
            </ChartContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
