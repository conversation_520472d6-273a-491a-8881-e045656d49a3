import { TrendingUp, <PERSON>, Building2, <PERSON>, BarChart3 } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardAction, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

export function SectionCards() {
  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Global Coffee Market Size</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">$102.15B</CardTitle>
          <CardAction>
            <Badge variant="outline">
              <Coffee className="size-3" />
              USD 2024
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Growing at 4.28% CAGR <TrendingUp className="size-4" />
          </div>
          <div className="text-muted-foreground">Expected to reach $155.64B by 2030</div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Mongolia Coffee Consumption</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">2.8kg</CardTitle>
          <CardAction>
            <Badge variant="outline">
              <Users className="size-3" />
              Per Person/Year
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Rising consumption trend <TrendingUp className="size-4" />
          </div>
          <div className="text-muted-foreground">15% increase from previous year</div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Ulaanbaatar Coffee Shops</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">847</CardTitle>
          <CardAction>
            <Badge variant="outline">
              <Building2 className="size-3" />
              Active Locations
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Market expansion ongoing <TrendingUp className="size-4" />
          </div>
          <div className="text-muted-foreground">Across 6 major districts</div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Average Coffee Price</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">₮7,200</CardTitle>
          <CardAction>
            <Badge variant="outline">
              <BarChart3 className="size-3" />
              Per Cup
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Competitive pricing range <TrendingUp className="size-4" />
          </div>
          <div className="text-muted-foreground">₮4,500 - ₮12,000 market range</div>
        </CardFooter>
      </Card>
    </div>
  );
}
