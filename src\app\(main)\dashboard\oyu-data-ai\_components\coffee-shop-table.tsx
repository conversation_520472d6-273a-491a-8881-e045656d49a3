"use client";

import React from "react";
import { z } from "zod";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useDataTableInstance } from "@/hooks/use-data-table-instance";

import { DataTable as DataTableNew } from "@/components/data-table/data-table";
import { DataTablePagination } from "@/components/data-table/data-table-pagination";
import { DataTableViewOptions } from "@/components/data-table/data-table-view-options";
import { withDndColumn } from "@/components/data-table/table-utils";

import { coffeeShopColumns } from "./coffee-shop-columns";
import { coffeeShopSchema } from "./coffee-shop-schema";

export function CoffeeShopTable({ data: initialData }: { data: z.infer<typeof coffeeShopSchema>[] }) {
  const [data, setData] = React.useState(() => initialData);
  const columns = withDndColumn(coffeeShopColumns);
  const table = useDataTableInstance({ 
    data, 
    columns, 
    getRowId: (row) => row.id.toString() 
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Coffee Shop Database</CardTitle>
        <CardDescription>
          Comprehensive data on coffee shops across Ulaanbaatar districts
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all-shops" className="w-full">
          <div className="flex items-center justify-between">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all-shops">All Shops</TabsTrigger>
              <TabsTrigger value="by-district">By District</TabsTrigger>
              <TabsTrigger value="by-type">By Type</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>
            <div className="ml-auto">
              <DataTableViewOptions table={table} />
            </div>
          </div>

          <TabsContent value="all-shops" className="relative flex flex-col gap-4 overflow-auto">
            <div className="overflow-hidden rounded-lg border">
              <DataTableNew dndEnabled table={table} columns={columns} onReorder={setData} />
            </div>
            <DataTablePagination table={table} />
          </TabsContent>

          <TabsContent value="by-district" className="flex flex-col">
            <div className="overflow-hidden rounded-lg border">
              <DataTableNew dndEnabled table={table} columns={columns} onReorder={setData} />
            </div>
            <DataTablePagination table={table} />
          </TabsContent>

          <TabsContent value="by-type" className="flex flex-col">
            <div className="overflow-hidden rounded-lg border">
              <DataTableNew dndEnabled table={table} columns={columns} onReorder={setData} />
            </div>
            <DataTablePagination table={table} />
          </TabsContent>

          <TabsContent value="analytics" className="flex flex-col">
            <div className="aspect-video w-full flex-1 rounded-lg border border-dashed flex items-center justify-center">
              <div className="text-center">
                <h3 className="text-lg font-semibold">Advanced Analytics</h3>
                <p className="text-muted-foreground">Coming soon - AI-powered insights and predictions</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
