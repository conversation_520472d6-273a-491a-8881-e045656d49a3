import { z } from "zod";

export const coffeeShopSchema = z.object({
  id: z.number(),
  name: z.string(),
  district: z.string(),
  address: z.string(),
  type: z.string(),
  avgPrice: z.number(),
  rating: z.number(),
  footTraffic: z.string(),
  openingYear: z.number(),
  size: z.string(),
  rentPerSqm: z.number(),
  employees: z.number(),
  dailyCustomers: z.number(),
  coordinates: z.array(z.number()),
  status: z.string(),
  competition: z.string(),
});

export type CoffeeShop = z.infer<typeof coffeeShopSchema>;
