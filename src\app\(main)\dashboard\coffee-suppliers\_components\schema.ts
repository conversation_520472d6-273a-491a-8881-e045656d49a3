import { z } from "zod";

// Coffee Bean Supplier Schema
export const coffeeSupplierSchema = z.object({
  id: z.number(),
  name: z.string(),
  email: z.string().email(),
  company: z.string(),
  tier: z.string(),
  status: z.string(),
  joinDate: z.string(),
  lastDelivery: z.string(),
  monthlyVolume: z.number(),
  pricePerKg: z.number(),
  origin: z.string(),
  qualityGrade: z.string(),
  certifications: z.array(z.string()),
  reliability: z.number(),
});

// Coffee Equipment Schema
export const coffeeEquipmentSchema = z.object({
  id: z.number(),
  category: z.string(),
  item: z.string(),
  supplier: z.string(),
  price: z.number(),
  specifications: z.string(),
  warranty: z.string(),
  deliveryTime: z.string(),
  popularity: z.number(),
});

// Menu Item Schema
export const menuItemSchema = z.object({
  id: z.number(),
  category: z.string(),
  item: z.string(),
  price: z.number(),
  cost: z.number(),
  margin: z.number(),
  popularity: z.number(),
  seasonal: z.boolean(),
});

export type CoffeeSupplier = z.infer<typeof coffeeSupplierSchema>;
export type CoffeeEquipment = z.infer<typeof coffeeEquipmentSchema>;
export type MenuItem = z.infer<typeof menuItemSchema>;
