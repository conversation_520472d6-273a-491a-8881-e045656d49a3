# OpenAI API Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-openai-api-key-here

# Application Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Optional: Database Configuration (if using external database)
# DATABASE_URL=your_database_connection_string

# Optional: Authentication (if implementing user authentication)
# NEXTAUTH_SECRET=your-nextauth-secret
# NEXTAUTH_URL=http://localhost:3000

# Optional: Analytics (if using analytics services)
# NEXT_PUBLIC_GA_ID=your-google-analytics-id
# NEXT_PUBLIC_MIXPANEL_TOKEN=your-mixpanel-token

# Optional: Monitoring (if using monitoring services)
# SENTRY_DSN=your-sentry-dsn
# VERCEL_ANALYTICS_ID=your-vercel-analytics-id
