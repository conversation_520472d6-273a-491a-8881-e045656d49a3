import { InsightCards } from "./_components/insight-cards";
import { OperationalCards } from "./_components/operational-cards";
import { OverviewCards } from "./_components/overview-cards";
import { TableCards } from "./_components/table-cards";

export default function Page() {
  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      {/* Header Section */}
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Coffee Supply Chain Management</h1>
        <p className="text-muted-foreground">
          Comprehensive supplier network, inventory management, and equipment sourcing for coffee shop operations in Mongolia
        </p>
      </div>

      {/* Supply Chain Overview Metrics */}
      <OverviewCards />

      {/* Coffee Industry Supply Insights */}
      <InsightCards />

      {/* Main Content Grid - Following Finance Dashboard Pattern */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {/* Operational Performance */}
        <div className="flex flex-col gap-4 lg:col-span-2">
          <div className="flex-1">
            <OperationalCards />
          </div>
        </div>

        {/* Supplier & Inventory Management */}
        <div className="flex flex-col gap-4 lg:col-span-1">
          <TableCards />
        </div>
      </div>
    </div>
  );
}
