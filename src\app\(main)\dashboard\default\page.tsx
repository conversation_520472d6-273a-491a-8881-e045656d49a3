import { ChartAreaInteractive } from "./_components/chart-area-interactive";
import { CoffeeMarketTable } from "./_components/coffee-market-table";
import coffeeMarketData from "./_components/coffee-market-data.json";
import { SectionCards } from "./_components/section-cards";

export default function Page() {
  return (
    <div className="@container/main flex flex-col gap-4 md:gap-6">
      {/* Header Section */}
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Coffee Market Intelligence</h1>
        <p className="text-muted-foreground">
          Global coffee industry trends, Mongolia market analysis, and comprehensive insights for coffee shop investment decisions
        </p>
      </div>

      {/* Key Metrics Cards */}
      <SectionCards />

      {/* Interactive Analytics Chart */}
      <ChartAreaInteractive />

      {/* Coffee Market Intelligence Table */}
      <CoffeeMarketTable data={coffeeMarketData} />
    </div>
  );
}
