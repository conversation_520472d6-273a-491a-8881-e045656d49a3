// Coffee Bean Supplier Data
export const coffeeBeanSuppliers = [
  {
    id: 1,
    name: "Ethiopian Premium Coffee Co.",
    email: "<EMAIL>",
    company: "Ethiopian Premium Coffee Co.",
    tier: "Premium Supplier",
    status: "Active",
    joinDate: "2024-01-15",
    lastDelivery: "2024-01-20",
    monthlyVolume: 2500, // kg
    pricePerKg: 18500, // MNT
    origin: "Ethiopia - Sidamo",
    qualityGrade: "Grade 1",
    certifications: ["Organic", "Fair Trade"],
    reliability: 95
  },
  {
    id: 2,
    name: "Colombian Coffee Exports",
    email: "<EMAIL>",
    company: "Colombian Coffee Exports",
    tier: "Premium Supplier",
    status: "Active",
    joinDate: "2024-01-12",
    lastDelivery: "2024-01-19",
    monthlyVolume: 3200,
    pricePerKg: 16800,
    origin: "Colombia - Huila",
    qualityGrade: "Grade 1",
    certifications: ["Rainforest Alliance"],
    reliability: 92
  },
  {
    id: 3,
    name: "Brazilian Coffee Direct",
    email: "<EMAIL>",
    company: "Brazilian Coffee Direct",
    tier: "Standard Supplier",
    status: "Active",
    joinDate: "2024-01-10",
    lastDelivery: "2024-01-18",
    monthlyVolume: 4500,
    pricePerKg: 14200,
    origin: "Brazil - Santos",
    qualityGrade: "Grade 2",
    certifications: ["UTZ Certified"],
    reliability: 88
  },
  {
    id: 4,
    name: "Guatemalan Highland Coffee",
    email: "<EMAIL>",
    company: "Guatemalan Highland Coffee",
    tier: "Premium Supplier",
    status: "Active",
    joinDate: "2024-01-08",
    lastDelivery: "2024-01-17",
    monthlyVolume: 1800,
    pricePerKg: 19200,
    origin: "Guatemala - Antigua",
    qualityGrade: "Grade 1",
    certifications: ["Organic", "Bird Friendly"],
    reliability: 94
  },
  {
    id: 5,
    name: "Mongolian Local Roasters",
    email: "<EMAIL>",
    company: "Mongolian Local Roasters",
    tier: "Local Supplier",
    status: "Active",
    joinDate: "2024-01-05",
    lastDelivery: "2024-01-16",
    monthlyVolume: 800,
    pricePerKg: 22000,
    origin: "Mongolia - Roasted Blend",
    qualityGrade: "Grade 2",
    certifications: ["Local Certified"],
    reliability: 85
  }
];

// Coffee Shop Equipment Data
export const coffeeEquipment = [
  {
    id: 1,
    category: "Espresso Machines",
    item: "La Marzocco Linea PB",
    supplier: "Coffee Equipment Mongolia",
    price: 15800000, // MNT
    specifications: "2 Group, Professional",
    warranty: "2 years",
    deliveryTime: "3-4 weeks",
    popularity: 92
  },
  {
    id: 2,
    category: "Coffee Grinders",
    item: "Mazzer Major Electronic",
    supplier: "Barista Equipment UB",
    price: 2400000,
    specifications: "83mm Burrs, Electronic",
    warranty: "1 year",
    deliveryTime: "2-3 weeks",
    popularity: 88
  },
  {
    id: 3,
    category: "Brewing Equipment",
    item: "Hario V60 Setup Complete",
    supplier: "Coffee Tools Mongolia",
    price: 180000,
    specifications: "Pour Over Complete Set",
    warranty: "6 months",
    deliveryTime: "1 week",
    popularity: 75
  },
  {
    id: 4,
    category: "Furniture",
    item: "Coffee Shop Table Set",
    supplier: "UB Furniture Co.",
    price: 450000,
    specifications: "4 Tables + 16 Chairs",
    warranty: "1 year",
    deliveryTime: "2 weeks",
    popularity: 82
  }
];

// Coffee Shop Menu Items and Pricing
export const menuItems = [
  {
    id: 1,
    category: "Espresso Drinks",
    item: "Espresso",
    price: 4500,
    cost: 1200,
    margin: 73,
    popularity: 85,
    seasonal: false
  },
  {
    id: 2,
    category: "Espresso Drinks", 
    item: "Cappuccino",
    price: 7200,
    cost: 2100,
    margin: 71,
    popularity: 95,
    seasonal: false
  },
  {
    id: 3,
    category: "Espresso Drinks",
    item: "Latte",
    price: 7800,
    cost: 2300,
    margin: 71,
    popularity: 92,
    seasonal: false
  },
  {
    id: 4,
    category: "Cold Drinks",
    item: "Iced Coffee",
    price: 6500,
    cost: 1800,
    margin: 72,
    popularity: 78,
    seasonal: true
  },
  {
    id: 5,
    category: "Pastries",
    item: "Croissant",
    price: 3200,
    cost: 900,
    margin: 72,
    popularity: 68,
    seasonal: false
  },
  {
    id: 6,
    category: "Sandwiches",
    item: "Club Sandwich",
    price: 8500,
    cost: 3200,
    margin: 62,
    popularity: 74,
    seasonal: false
  }
];

// Coffee Shop Setup Costs
export const setupCosts = {
  equipment: {
    espressoMachine: 15800000,
    grinder: 2400000,
    brewingEquipment: 800000,
    refrigeration: 1200000,
    furniture: 2500000,
    total: 22700000
  },
  licensing: {
    businessLicense: 150000,
    foodServicePermit: 80000,
    signagePermit: 45000,
    total: 275000
  },
  interior: {
    renovation: 8500000,
    lighting: 1200000,
    flooring: 2800000,
    decoration: 1500000,
    total: 14000000
  },
  monthlyOperational: {
    rent: 2500000,
    utilities: 450000,
    staff: 3200000,
    supplies: 1800000,
    total: 7950000
  }
};

// Supply Chain Metrics
export const supplyChainMetrics = {
  totalSuppliers: 28,
  activeSuppliers: 23,
  monthlyProcurement: 12500000, // MNT
  avgDeliveryTime: 2.8, // weeks
  avgSupplierReliability: 89.2,
  totalEquipmentValue: 45600000,
  avgSetupCost: 44925000,
  monthlyOperationalCost: 7950000
};
