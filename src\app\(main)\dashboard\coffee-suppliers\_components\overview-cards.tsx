"use client";

import { TrendingUp, Package, Clock, Star } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardAction, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

import { supplyChainMetrics } from "./coffee-supply-chain.config";

export function OverviewCards() {
  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4">
      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Active Coffee Suppliers</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">{supplyChainMetrics.activeSuppliers}</CardTitle>
          <CardAction>
            <Badge variant="outline">
              <Package className="size-3" />
              Verified
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Reliable supply network <TrendingUp className="size-4" />
          </div>
          <div className="text-muted-foreground">From {supplyChainMetrics.totalSuppliers} total suppliers</div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Monthly Procurement</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">₮{(supplyChainMetrics.monthlyProcurement / 1000000).toFixed(1)}M</CardTitle>
          <CardAction>
            <Badge variant="outline">
              <TrendingUp className="size-3" />
              +12.5%
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Growing procurement volume <TrendingUp className="size-4" />
          </div>
          <div className="text-muted-foreground">Coffee beans and supplies</div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Avg Delivery Time</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">{supplyChainMetrics.avgDeliveryTime}</CardTitle>
          <CardAction>
            <Badge variant="outline">
              <Clock className="size-3" />
              Weeks
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Efficient logistics <Clock className="size-4" />
          </div>
          <div className="text-muted-foreground">International to Mongolia</div>
        </CardFooter>
      </Card>

      <Card className="@container/card">
        <CardHeader>
          <CardDescription>Supplier Reliability</CardDescription>
          <CardTitle className="text-2xl font-semibold tabular-nums @[250px]/card:text-3xl">{supplyChainMetrics.avgSupplierReliability}%</CardTitle>
          <CardAction>
            <Badge variant="outline">
              <Star className="size-3" />
              Rating
            </Badge>
          </CardAction>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1.5 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            High quality standards <Star className="size-4" />
          </div>
          <div className="text-muted-foreground">Certified suppliers only</div>
        </CardFooter>
      </Card>
    </div>
  );
}
