"use client";

import { TrendingUp, TrendingDown, Coffee, DollarSign, Target } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

import { setupCosts, menuItems } from "./coffee-supply-chain.config";

export function InsightCards() {
  // Calculate average margin from menu items
  const avgMargin = menuItems.reduce((sum, item) => sum + item.margin, 0) / menuItems.length;

  // Calculate most popular item
  const mostPopular = menuItems.reduce((prev, current) =>
    (prev.popularity > current.popularity) ? prev : current,
    menuItems[0]
  );

  return (
    <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
      <Card className="shadow-xs">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coffee className="h-5 w-5" />
            Coffee Shop Setup Investment
          </CardTitle>
          <CardDescription>
            Complete cost breakdown for opening a coffee shop in Ulaanbaatar
          </CardDescription>
          <CardAction>
            <Select defaultValue="ulaanbaatar">
              <SelectTrigger>
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ulaanbaatar">Ulaanbaatar</SelectItem>
                <SelectItem value="erdenet">Erdenet</SelectItem>
                <SelectItem value="darkhan">Darkhan</SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent>
          <Separator />
          <div className="space-y-4 pt-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Equipment</span>
                <span className="text-sm font-semibold">₮{(setupCosts.equipment.total / 1000000).toFixed(1)}M</span>
              </div>
              <Progress value={50} className="h-2" />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Interior & Renovation</span>
                <span className="text-sm font-semibold">₮{(setupCosts.interior.total / 1000000).toFixed(1)}M</span>
              </div>
              <Progress value={31} className="h-2" />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Licensing & Permits</span>
                <span className="text-sm font-semibold">₮{(setupCosts.licensing.total / 1000).toFixed(0)}K</span>
              </div>
              <Progress value={1} className="h-2" />
            </div>

            <Separator />
            <div className="flex flex-col items-start justify-between gap-2 py-5 md:flex-row md:items-stretch md:gap-0">
              <div className="flex flex-1 items-center justify-center gap-2">
                <div className="flex size-10 shrink-0 items-center justify-center rounded-full border">
                  <Target className="stroke-chart-1 size-6" />
                </div>
                <div>
                  <p className="text-muted-foreground text-xs uppercase">Total Investment</p>
                  <p className="font-medium tabular-nums">₮{((setupCosts.equipment.total + setupCosts.interior.total + setupCosts.licensing.total) / 1000000).toFixed(1)}M</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-xs">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Menu Performance Analytics
          </CardTitle>
          <CardDescription>
            Profit margins and popularity metrics for coffee shop menu items
          </CardDescription>
          <CardAction>
            <Select defaultValue="all-items">
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-items">All Items</SelectItem>
                <SelectItem value="beverages">Beverages</SelectItem>
                <SelectItem value="food">Food</SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent>
          <Separator />
          <div className="space-y-4 pt-4">
            <div className="flex flex-col items-start justify-between gap-4 md:flex-row md:items-stretch md:gap-0">
              <div className="flex flex-1 items-center justify-center gap-2">
                <div className="flex size-10 shrink-0 items-center justify-center rounded-full border">
                  <TrendingUp className="stroke-chart-1 size-6" />
                </div>
                <div>
                  <p className="text-muted-foreground text-xs uppercase">Avg Profit Margin</p>
                  <p className="font-medium tabular-nums">{avgMargin.toFixed(1)}%</p>
                </div>
              </div>
              <div className="flex flex-1 items-center justify-center gap-2">
                <div className="flex size-10 shrink-0 items-center justify-center rounded-full border">
                  <Coffee className="stroke-chart-2 size-6" />
                </div>
                <div>
                  <p className="text-muted-foreground text-xs uppercase">Most Popular</p>
                  <p className="font-medium">{mostPopular.item}</p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="text-sm font-medium">Top Menu Categories by Margin:</div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Espresso Drinks</span>
                  <Badge variant="secondary">72% avg margin</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Pastries</span>
                  <Badge variant="secondary">72% avg margin</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Sandwiches</span>
                  <Badge variant="outline">62% avg margin</Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-xs">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Monthly Operational Costs
          </CardTitle>
          <CardDescription>
            Recurring monthly expenses for coffee shop operations
          </CardDescription>
          <CardAction>
            <Select defaultValue="monthly">
              <SelectTrigger>
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent>
          <Separator />
          <div className="space-y-4 pt-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Staff Salaries</span>
                <span className="text-sm font-semibold">₮{(setupCosts.monthlyOperational.staff / 1000000).toFixed(1)}M</span>
              </div>
              <Progress value={40} className="h-2" />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Rent</span>
                <span className="text-sm font-semibold">₮{(setupCosts.monthlyOperational.rent / 1000000).toFixed(1)}M</span>
              </div>
              <Progress value={31} className="h-2" />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Supplies & Inventory</span>
                <span className="text-sm font-semibold">₮{(setupCosts.monthlyOperational.supplies / 1000000).toFixed(1)}M</span>
              </div>
              <Progress value={23} className="h-2" />

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Utilities</span>
                <span className="text-sm font-semibold">₮{(setupCosts.monthlyOperational.utilities / 1000).toFixed(0)}K</span>
              </div>
              <Progress value={6} className="h-2" />
            </div>

            <Separator />
            <div className="flex flex-col items-start justify-between gap-2 py-5 md:flex-row md:items-stretch md:gap-0">
              <div className="flex flex-1 items-center justify-center gap-2">
                <div className="flex size-10 shrink-0 items-center justify-center rounded-full border">
                  <DollarSign className="stroke-chart-3 size-6" />
                </div>
                <div>
                  <p className="text-muted-foreground text-xs uppercase">Monthly Total</p>
                  <p className="font-medium tabular-nums">₮{(setupCosts.monthlyOperational.total / 1000000).toFixed(1)}M</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-xs">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Coffee className="h-5 w-5" />
            Coffee Bean Price Trends
          </CardTitle>
          <CardDescription>
            Current pricing and trends for different coffee bean origins
          </CardDescription>
          <CardAction>
            <Select defaultValue="premium">
              <SelectTrigger>
                <SelectValue placeholder="Select grade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="premium">Premium Grade</SelectItem>
                <SelectItem value="commercial">Commercial Grade</SelectItem>
                <SelectItem value="specialty">Specialty Grade</SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent>
          <Separator />
          <div className="space-y-4 pt-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium">Ethiopian Premium</div>
                  <div className="text-xs text-muted-foreground">Grade 1, Organic</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold">₮18,500/kg</div>
                  <Badge variant="outline" className="text-xs">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +5%
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium">Colombian Huila</div>
                  <div className="text-xs text-muted-foreground">Grade 1, Rainforest</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold">₮16,800/kg</div>
                  <Badge variant="outline" className="text-xs">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    +2%
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium">Brazilian Santos</div>
                  <div className="text-xs text-muted-foreground">Grade 2, UTZ</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold">₮14,200/kg</div>
                  <Badge variant="secondary" className="text-xs">
                    <TrendingDown className="h-3 w-3 mr-1" />
                    -1%
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
