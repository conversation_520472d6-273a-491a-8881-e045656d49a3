import { ChartConfig } from "@/components/ui/chart";

// District Analysis Data
export const districtAnalysisData = [
  { district: "Sukhbaatar", coffeeShops: 15, population: 145000, density: 0.103, avgRent: 46500 },
  { district: "Chingeltei", coffeeShops: 22, population: 185000, density: 0.119, avgRent: 42000 },
  { district: "Bayanzurkh", coffeeShops: 18, population: 280000, density: 0.064, avgRent: 26500 },
  { district: "Khan-Uul", coffeeShops: 12, population: 165000, density: 0.073, avgRent: 32000 },
  { district: "Bayangol", coffeeShops: 14, population: 195000, density: 0.072, avgRent: 32500 },
  { district: "Songinokhairkhan", coffeeShops: 8, population: 320000, density: 0.025, avgRent: 19000 }
];

export const districtChartConfig = {
  coffeeShops: {
    label: "Coffee Shops",
    color: "var(--chart-1)",
  },
  density: {
    label: "Density (per 1000)",
    color: "var(--chart-2)",
  },
  avgRent: {
    label: "Avg Rent (₮/sqm)",
    color: "var(--chart-3)",
  },
} as ChartConfig;

// Price Distribution Data
export const priceDistributionData = [
  { priceRange: "4000-5000₮", count: 8, percentage: 22 },
  { priceRange: "5000-6000₮", count: 12, percentage: 33 },
  { priceRange: "6000-7000₮", count: 9, percentage: 25 },
  { priceRange: "7000-8000₮", count: 5, percentage: 14 },
  { priceRange: "8000+₮", count: 2, percentage: 6 }
];

export const priceChartConfig = {
  count: {
    label: "Number of Shops",
    color: "var(--chart-1)",
  },
  percentage: {
    label: "Percentage",
    color: "var(--chart-2)",
  },
} as ChartConfig;

// Monthly Trends Data
export const monthlyTrendsData = [
  { month: "Jan", revenue: 2800000, customers: 8500, newShops: 0 },
  { month: "Feb", revenue: 2600000, customers: 7800, newShops: 1 },
  { month: "Mar", revenue: 3200000, customers: 9200, newShops: 2 },
  { month: "Apr", revenue: 3800000, customers: 10500, newShops: 1 },
  { month: "May", revenue: 4200000, customers: 11800, newShops: 3 },
  { month: "Jun", revenue: 4500000, customers: 12500, newShops: 2 },
  { month: "Jul", revenue: 4800000, customers: 13200, newShops: 1 },
  { month: "Aug", revenue: 4600000, customers: 12800, newShops: 0 },
  { month: "Sep", revenue: 4100000, customers: 11500, newShops: 2 },
  { month: "Oct", revenue: 3900000, customers: 10800, newShops: 1 },
  { month: "Nov", revenue: 3400000, customers: 9500, newShops: 0 },
  { month: "Dec", revenue: 3600000, customers: 10200, newShops: 1 }
];

export const trendsChartConfig = {
  revenue: {
    label: "Revenue (₮)",
    color: "var(--chart-1)",
  },
  customers: {
    label: "Customers",
    color: "var(--chart-2)",
  },
  newShops: {
    label: "New Shops",
    color: "var(--chart-3)",
  },
} as ChartConfig;

// Competition Analysis Data
export const competitionData = [
  { type: "International Chain", count: 8, marketShare: 45, avgPrice: 8850 },
  { type: "Local Chain", count: 12, marketShare: 35, avgPrice: 7200 },
  { type: "Independent", count: 25, marketShare: 20, avgPrice: 5800 }
];

export const competitionChartConfig = {
  count: {
    label: "Number of Shops",
    color: "var(--chart-1)",
  },
  marketShare: {
    label: "Market Share (%)",
    color: "var(--chart-2)",
  },
  avgPrice: {
    label: "Average Price (₮)",
    color: "var(--chart-3)",
  },
} as ChartConfig;

// Foot Traffic Analysis
export const footTrafficData = [
  { location: "Government Square", traffic: "Very High", score: 95, shops: 3 },
  { location: "Central Tower Area", traffic: "Very High", score: 92, shops: 4 },
  { location: "Peace Avenue", traffic: "High", score: 85, shops: 6 },
  { location: "Chinggis Avenue", traffic: "High", score: 82, shops: 5 },
  { location: "Seoul Street", traffic: "High", score: 80, shops: 3 },
  { location: "Zaisan Area", traffic: "Medium", score: 65, shops: 2 },
  { location: "Buyant-Ukhaa", traffic: "Low", score: 45, shops: 2 },
  { location: "New Development", traffic: "Low", score: 40, shops: 1 }
];

export const trafficChartConfig = {
  score: {
    label: "Traffic Score",
    color: "var(--chart-1)",
  },
  shops: {
    label: "Coffee Shops",
    color: "var(--chart-2)",
  },
} as ChartConfig;

// Investment Opportunities
export const investmentOpportunities = [
  {
    district: "Songinokhairkhan",
    opportunity: "High",
    reason: "Low competition, growing population",
    avgRent: 19000,
    competition: "Low",
    growthPotential: 85
  },
  {
    district: "Bayanzurkh",
    opportunity: "Medium",
    reason: "Large population, moderate competition",
    avgRent: 26500,
    competition: "Medium",
    growthPotential: 70
  },
  {
    district: "Khan-Uul",
    opportunity: "Medium",
    reason: "Diplomatic area, higher income",
    avgRent: 32000,
    competition: "Medium",
    growthPotential: 75
  }
];

// Key Market Insights
export const marketInsights = {
  totalMarketSize: "₮1.2B annually",
  averagePrice: "₮6,800",
  marketGrowth: "+15% YoY",
  totalShops: 89,
  averageCustomersPerDay: 180,
  peakHours: "8-10 AM, 2-4 PM",
  seasonalTrend: "Summer peak (+25%), Winter low (-15%)",
  preferredTypes: ["Americano (35%)", "Latte (28%)", "Cappuccino (20%)"]
};
