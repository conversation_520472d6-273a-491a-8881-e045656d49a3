import { ColumnDef } from "@tanstack/react-table";
import { Star, MapPin, Users, TrendingUp } from "lucide-react";

import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

import { CoffeeShop } from "./coffee-shop-schema";

export const coffeeShopColumns: ColumnDef<CoffeeShop>[] = [
  {
    accessorKey: "name",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Coffee Shop" />,
    cell: ({ row }) => {
      const shop = row.original;
      return (
        <div className="flex flex-col">
          <div className="font-medium">{shop.name}</div>
          <div className="text-sm text-muted-foreground flex items-center gap-1">
            <MapPin className="h-3 w-3" />
            {shop.district}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "type",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Type" />,
    cell: ({ row }) => {
      const type = row.getValue("type") as string;
      const variant = 
        type === "International Chain" ? "default" :
        type === "Local Chain" ? "secondary" : "outline";
      
      return <Badge variant={variant}>{type}</Badge>;
    },
  },
  {
    accessorKey: "avgPrice",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Avg Price" />,
    cell: ({ row }) => {
      const price = row.getValue("avgPrice") as number;
      return <div className="font-medium">₮{price.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "rating",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Rating" />,
    cell: ({ row }) => {
      const rating = row.getValue("rating") as number;
      return (
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="font-medium">{rating}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "dailyCustomers",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Daily Customers" />,
    cell: ({ row }) => {
      const customers = row.getValue("dailyCustomers") as number;
      return (
        <div className="flex items-center gap-1">
          <Users className="h-4 w-4 text-muted-foreground" />
          <span>{customers}</span>
        </div>
      );
    },
  },
  {
    accessorKey: "footTraffic",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Foot Traffic" />,
    cell: ({ row }) => {
      const traffic = row.getValue("footTraffic") as string;
      const variant = 
        traffic === "Very High" ? "default" :
        traffic === "High" ? "secondary" :
        traffic === "Medium" ? "outline" : "destructive";
      
      return <Badge variant={variant}>{traffic}</Badge>;
    },
  },
  {
    accessorKey: "competition",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Competition" />,
    cell: ({ row }) => {
      const competition = row.getValue("competition") as string;
      const variant = 
        competition === "Very High" ? "destructive" :
        competition === "High" ? "secondary" :
        competition === "Medium" ? "outline" : "default";
      
      return <Badge variant={variant}>{competition}</Badge>;
    },
  },
  {
    accessorKey: "rentPerSqm",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Rent/sqm" />,
    cell: ({ row }) => {
      const rent = row.getValue("rentPerSqm") as number;
      return <div className="text-sm">₮{rent.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "openingYear",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Opened" />,
    cell: ({ row }) => {
      const year = row.getValue("openingYear") as number;
      const yearsOld = new Date().getFullYear() - year;
      return (
        <div className="text-sm">
          {year} ({yearsOld}y)
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const shop = row.original;
      return (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            View Details
          </Button>
          <Button variant="ghost" size="sm">
            <TrendingUp className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];
