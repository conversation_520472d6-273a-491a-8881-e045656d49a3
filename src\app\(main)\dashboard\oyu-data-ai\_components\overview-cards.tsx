"use client";

import { Coffee, TrendingUp, MapPin, DollarSign, Users, Building2 } from "lucide-react";
import { Area, AreaChart, Bar, BarChart, XAxis, YAxis } from "recharts";

import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { formatCurrency } from "@/lib/utils";

import { monthlyTrendsData, trendsChartConfig, marketInsights } from "./market-data";
import coffeeShopsData from "./coffee-shops-data.json";

export function OverviewCards() {
  const totalShops = coffeeShopsData.length;
  const avgPrice = Math.round(coffeeShopsData.reduce((sum, shop) => sum + shop.avgPrice, 0) / totalShops);
  const totalCustomers = coffeeShopsData.reduce((sum, shop) => sum + shop.dailyCustomers, 0);
  const avgRating = (coffeeShopsData.reduce((sum, shop) => sum + shop.rating, 0) / totalShops).toFixed(1);

  // Calculate growth from last 3 months
  const recentMonths = monthlyTrendsData.slice(-3);
  const avgRecentRevenue = recentMonths.reduce((sum, month) => sum + month.revenue, 0) / 3;
  const previousMonths = monthlyTrendsData.slice(-6, -3);
  const avgPreviousRevenue = previousMonths.reduce((sum, month) => sum + month.revenue, 0) / 3;
  const growthRate = ((avgRecentRevenue - avgPreviousRevenue) / avgPreviousRevenue * 100).toFixed(1);

  return (
    <div className="grid grid-cols-1 gap-4 *:data-[slot=card]:shadow-xs sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {/* Total Coffee Shops */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Coffee Shops</CardTitle>
          <Coffee className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalShops}</div>
          <p className="text-xs text-muted-foreground">
            +3 from last month
          </p>
        </CardContent>
      </Card>

      {/* Average Price */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Price</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">₮{avgPrice.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            +5.2% from last quarter
          </p>
        </CardContent>
      </Card>

      {/* Daily Customers */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Daily Customers</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalCustomers.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            Across all locations
          </p>
        </CardContent>
      </Card>

      {/* Market Growth */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Market Growth</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">+{growthRate}%</div>
          <p className="text-xs text-muted-foreground">
            Last 3 months vs previous
          </p>
        </CardContent>
        <CardFooter className="p-0">
          <ChartContainer className="h-16 w-full" config={trendsChartConfig}>
            <AreaChart data={monthlyTrendsData.slice(-6)}>
              <Area
                type="monotone"
                dataKey="revenue"
                stroke="var(--chart-1)"
                fill="var(--chart-1)"
                fillOpacity={0.2}
                strokeWidth={2}
              />
            </AreaChart>
          </ChartContainer>
        </CardFooter>
      </Card>

      {/* Average Rating */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{avgRating}/5.0</div>
          <p className="text-xs text-muted-foreground">
            Customer satisfaction
          </p>
        </CardContent>
      </Card>

      {/* Districts Covered */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Districts</CardTitle>
          <MapPin className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">6</div>
          <p className="text-xs text-muted-foreground">
            Ulaanbaatar districts
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
