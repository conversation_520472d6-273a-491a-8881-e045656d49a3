# Oyu-Data-AI Platform

<div align="center">
  <h3>🚀 Market Intelligence Platform for Mongolia</h3>
  <p>Cutting-edge data intelligence platform offering structured, curated, and up-to-date datasets for market insights in Mongolia</p>
  
  [![Next.js](https://img.shields.io/badge/Next.js-15.4.3-black?style=flat-square&logo=next.js)](https://nextjs.org/)
  [![React](https://img.shields.io/badge/React-19.1.0-blue?style=flat-square&logo=react)](https://reactjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.8.3-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4.1.5-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
  [![OpenAI](https://img.shields.io/badge/OpenAI-API-412991?style=flat-square&logo=openai)](https://openai.com/)
</div>

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Technology Stack](#technology-stack)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Environment Configuration](#environment-configuration)
- [Usage](#usage)
- [AI Chatbot Features](#ai-chatbot-features)
- [Development](#development)
- [Project Structure](#project-structure)
- [Contributing](#contributing)
- [License](#license)

## 🌟 Overview

Oyu-Data-AI is a comprehensive market intelligence platform specifically designed for the Mongolian market. The platform provides businesses, investors, and entrepreneurs with reliable, structured data to make informed decisions about market opportunities, particularly in the coffee shop and retail sectors in Ulaanbaatar.

### Key Capabilities

- **Real-time Market Data**: Access to comprehensive coffee shop market data across all districts in Ulaanbaatar
- **AI-Powered Insights**: Intelligent chatbot assistant powered by OpenAI for instant market analysis
- **Investment Opportunities**: Detailed analysis of high-potential areas for business expansion
- **Competitive Intelligence**: District-wise competition analysis and market positioning data
- **Rental Market Data**: Up-to-date commercial real estate pricing and availability

## ✨ Features

### 🤖 AI Chatbot Assistant
- **Intelligent Conversations**: Natural language processing for market data queries
- **Real-time Responses**: Instant answers to market intelligence questions
- **Conversation History**: Persistent chat history with local storage
- **Rate Limiting**: Built-in security with request throttling
- **Error Handling**: Robust error management with fallback responses
- **Responsive Design**: Mobile-friendly interface with dark mode support

### 📊 Market Intelligence
- **District Analysis**: Comprehensive data for all Ulaanbaatar districts
- **Competition Mapping**: Detailed competitor analysis and market saturation data
- **Investment Scoring**: AI-driven investment opportunity rankings
- **Foot Traffic Data**: High-traffic location identification and scoring
- **Growth Trends**: Historical and projected market growth analysis

### 🔒 Security Features
- **Input Validation**: Comprehensive XSS and injection protection
- **Rate Limiting**: API request throttling to prevent abuse
- **Error Sanitization**: Secure error handling without data exposure
- **Request Timeout**: Automatic timeout protection for API calls

## 🛠 Technology Stack

### Frontend
- **Next.js 15.4.3** - React framework with App Router
- **React 19.1.0** - UI library with latest features
- **TypeScript 5.8.3** - Type-safe development
- **Tailwind CSS 4.1.5** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **Lucide React** - Beautiful icon library

### Backend & AI
- **Next.js API Routes** - Serverless API endpoints
- **OpenAI API** - GPT-3.5-turbo for intelligent responses
- **Zod** - Runtime type validation
- **Rate Limiting** - In-memory request throttling

### Development Tools
- **ESLint** - Code linting and quality enforcement
- **Prettier** - Code formatting
- **Husky** - Git hooks for quality gates
- **TypeScript** - Static type checking

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 18.0 or higher)
- **npm** or **yarn** package manager
- **Git** for version control
- **OpenAI API Key** (required for AI chatbot functionality)

## 🚀 Installation

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/oyu-data-ai.git
cd oyu-data-ai
```

### 2. Install Dependencies

```bash
npm install
# or
yarn install
```

### 3. Environment Setup

Create a `.env.local` file in the root directory:

```bash
cp .env.example .env.local
```

## ⚙️ Environment Configuration

### Required Environment Variables

Create a `.env.local` file with the following variables:

```env
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Development Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Getting Your OpenAI API Key

1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to **API Keys** section
4. Click **Create new secret key**
5. Copy the key and add it to your `.env.local` file

⚠️ **Important**: Never commit your `.env.local` file to version control. The file is already included in `.gitignore`.

## 🎯 Usage

### Development Server

Start the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

### Production Build

Build the application for production:

```bash
npm run build
npm start
# or
yarn build
yarn start
```

### Code Quality

Run linting and formatting:

```bash
# Lint code
npm run lint

# Format code
npm run format

# Check formatting
npm run format:check
```

## 🤖 AI Chatbot Features

### Accessing the Chatbot

1. Look for the floating chat button in the bottom-right corner
2. Click to open the AI assistant
3. Start asking questions about Mongolia's coffee shop market

### Sample Questions

Try asking the chatbot:

- "Which district has the lowest competition?"
- "What's the average rent in Sukhbaatar district?"
- "Show me investment opportunities"
- "Which areas have high foot traffic?"
- "What's the market growth trend?"

### Chatbot Capabilities

- **Natural Language Processing**: Understands conversational queries
- **Market Data Access**: Real-time access to comprehensive market database
- **Investment Analysis**: Provides investment recommendations with data backing
- **District Comparisons**: Comparative analysis across Ulaanbaatar districts
- **Trend Analysis**: Historical and projected market trends

## 🔧 Development

### Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   │   └── chat/          # AI chatbot endpoint
│   ├── dashboard/         # Dashboard pages
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── ai-chatbot/       # Chatbot components
│   └── ui/               # Reusable UI components
├── config/               # Application configuration
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── stores/               # State management
├── styles/               # Global styles
└── types/                # TypeScript type definitions
```

### Key Components

- **AIChatbot** (`src/components/ai-chatbot/ai-chatbot.tsx`): Main chatbot interface
- **ChatbotTrigger** (`src/components/ai-chatbot/chatbot-trigger.tsx`): Floating chat button
- **Chat API** (`src/app/api/chat/route.ts`): Backend API for AI responses

### Development Guidelines

1. **Code Style**: Follow the established ESLint and Prettier configurations
2. **Type Safety**: Use TypeScript for all new code
3. **Component Structure**: Follow the established component patterns
4. **API Design**: Maintain RESTful API conventions
5. **Security**: Always validate inputs and sanitize outputs

### Adding New Features

1. Create feature branch: `git checkout -b feature/your-feature-name`
2. Implement changes following existing patterns
3. Add appropriate tests if applicable
4. Update documentation as needed
5. Submit pull request with detailed description

## 📁 Project Structure

```
oyu-data-ai/
├── public/                 # Static assets
├── src/                   # Source code
│   ├── app/              # Next.js App Router
│   ├── components/       # React components
│   ├── config/          # Configuration files
│   ├── hooks/           # Custom hooks
│   ├── lib/             # Utilities
│   ├── stores/          # State management
│   ├── styles/          # Styling
│   └── types/           # Type definitions
├── .env.local           # Environment variables (create this)
├── .gitignore          # Git ignore rules
├── package.json        # Dependencies and scripts
├── tailwind.config.js  # Tailwind configuration
├── tsconfig.json       # TypeScript configuration
└── README.md           # This file
```

## 🤝 Contributing

We welcome contributions to the Oyu-Data-AI platform! Please follow these guidelines:

### Getting Started

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Code Standards

- Follow TypeScript best practices
- Use meaningful variable and function names
- Add comments for complex logic
- Ensure responsive design
- Test on multiple browsers

### Pull Request Process

1. Update documentation for any new features
2. Ensure all tests pass
3. Update the README if needed
4. Request review from maintainers

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Add environment variables in Vercel dashboard
4. Deploy automatically on every push

### Manual Deployment

```bash
npm run build
npm start
```

### Environment Variables for Production

Ensure these are set in your production environment:

```env
OPENAI_API_KEY=your_production_openai_key
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

## 🔍 Troubleshooting

### Common Issues

**1. OpenAI API Key Not Working**
- Verify your API key is correct
- Check if you have sufficient credits
- Ensure the key has proper permissions

**2. Build Errors**
- Clear node_modules: `rm -rf node_modules && npm install`
- Check Node.js version compatibility
- Verify all environment variables are set

**3. Chatbot Not Responding**
- Check browser console for errors
- Verify API endpoint is accessible
- Check rate limiting status

**4. Styling Issues**
- Clear browser cache
- Check Tailwind CSS compilation
- Verify component imports

### Getting Help

- Check the [Issues](https://github.com/your-username/oyu-data-ai/issues) page
- Review the documentation
- Contact the development team

## 📊 Performance

### Optimization Features

- **Code Splitting**: Automatic code splitting with Next.js
- **Image Optimization**: Built-in Next.js image optimization
- **Caching**: Intelligent caching strategies
- **Bundle Analysis**: Use `npm run analyze` to analyze bundle size

### Performance Monitoring

Monitor your application performance:

```bash
# Analyze bundle size
npm run build
npm run analyze
```

## 🔐 Security

### Security Measures Implemented

- **Input Validation**: All user inputs are validated and sanitized
- **Rate Limiting**: API endpoints are protected against abuse
- **XSS Protection**: Content Security Policy and input sanitization
- **Error Handling**: Secure error messages without data exposure
- **HTTPS**: Enforce HTTPS in production

### Security Best Practices

1. Keep dependencies updated
2. Use environment variables for secrets
3. Implement proper authentication (when needed)
4. Regular security audits
5. Monitor for vulnerabilities

## 📈 Analytics & Monitoring

### Built-in Monitoring

- Request logging with performance metrics
- Error tracking and reporting
- Rate limiting monitoring
- API usage statistics

### Adding Analytics

To add Google Analytics or other tracking:

1. Install analytics package
2. Add tracking ID to environment variables
3. Implement tracking in `_app.tsx`

## 🌐 Internationalization

The platform is designed with internationalization in mind:

- English (default)
- Mongolian (planned)
- Support for RTL languages

To add new languages:

1. Install `next-i18next`
2. Create translation files
3. Configure routing for locales

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

---

<div align="center">
  <p>Built with ❤️ for the Mongolian business community</p>
  <p>© 2024 Oyu-Data-AI Platform. All rights reserved.</p>
</div>
