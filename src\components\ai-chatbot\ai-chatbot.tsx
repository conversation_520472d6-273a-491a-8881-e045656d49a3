"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import { Send, Bot, User, Minimize2, Maximize2, X, Clock, AlertCircle, Loader2 } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
  isError?: boolean;
}

interface AIChatbotProps {
  isOpen: boolean;
  onToggle: () => void;
  onClose: () => void;
}

// Rate limiting configuration
const RATE_LIMIT = {
  maxMessages: 10,
  windowMs: 60000, // 1 minute
};

export function AIChatbot({ isOpen, onToggle, onClose }: AIChatbotProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      role: "assistant",
      content: "Hello! I'm your Oyu-Data-AI assistant. I can help you analyze coffee shop market data in Ulaanbaatar. Ask me questions like 'Which district has the lowest competition?' or 'What's the average rent in Sukhbaatar district?'",
      timestamp: new Date(),
    },
  ]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [messageHistory, setMessageHistory] = useState<Message[]>([]);
  const [rateLimitInfo, setRateLimitInfo] = useState({ count: 0, resetTime: Date.now() });
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Load conversation history from localStorage on mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('oyu-chatbot-history');
    if (savedHistory) {
      try {
        const parsedHistory = JSON.parse(savedHistory);
        setMessageHistory(parsedHistory);
      } catch (error) {
        console.warn('Failed to load chat history:', error);
      }
    }
  }, []);

  // Save conversation history to localStorage
  useEffect(() => {
    if (messages.length > 1) { // Don't save just the initial message
      localStorage.setItem('oyu-chatbot-history', JSON.stringify(messages));
    }
  }, [messages]);

  // Focus input when chatbot opens
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, isMinimized]);

  // Rate limiting check
  const checkRateLimit = useCallback(() => {
    const now = Date.now();
    if (now - rateLimitInfo.resetTime > RATE_LIMIT.windowMs) {
      setRateLimitInfo({ count: 0, resetTime: now });
      return true;
    }
    return rateLimitInfo.count < RATE_LIMIT.maxMessages;
  }, [rateLimitInfo]);

  // Input validation
  const validateInput = useCallback((text: string): string | null => {
    if (!text.trim()) return "Please enter a message";
    if (text.length > 1000) return "Message is too long (max 1000 characters)";
    if (text.trim().length < 2) return "Message is too short (min 2 characters)";

    // Basic XSS prevention
    const dangerousPatterns = /<script|javascript:|data:|vbscript:/i;
    if (dangerousPatterns.test(text)) return "Invalid characters detected";

    return null;
  }, []);

  // Format timestamp for display
  const formatTimestamp = useCallback((date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).format(date);
  }, []);

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return;

    // Clear any previous errors
    setError(null);

    // Validate input
    const validationError = validateInput(input);
    if (validationError) {
      setError(validationError);
      return;
    }

    // Check rate limiting
    if (!checkRateLimit()) {
      setError(`Rate limit exceeded. Please wait before sending another message.`);
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: input.trim(),
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);
    setIsTyping(true);

    // Update rate limit counter
    setRateLimitInfo(prev => ({ ...prev, count: prev.count + 1 }));

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: input.trim(),
          context: "coffee_shop_market_ulaanbaatar",
          timestamp: Date.now(),
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data = await response.json();

      if (!data.message) {
        throw new Error("Invalid response format");
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.message,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
      console.error("Chat error:", error);

      let errorContent = "I apologize, but I'm having trouble processing your request right now. Please try again later.";

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorContent = "Request timed out. Please try again with a shorter message.";
        } else if (error.message.includes('Rate limit')) {
          errorContent = "Too many requests. Please wait a moment before trying again.";
        } else if (error.message.includes('Network')) {
          errorContent = "Network error. Please check your connection and try again.";
        }
      }

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: errorContent,
        timestamp: new Date(),
        isError: true,
      };

      setMessages((prev) => [...prev, errorMessage]);
      setError("Failed to send message. Please try again.");
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearConversation = useCallback(() => {
    setMessages([{
      id: "1",
      role: "assistant",
      content: "Hello! I'm your Oyu-Data-AI assistant. I can help you analyze coffee shop market data in Ulaanbaatar. Ask me questions like 'Which district has the lowest competition?' or 'What's the average rent in Sukhbaatar district?'",
      timestamp: new Date(),
    }]);
    localStorage.removeItem('oyu-chatbot-history');
    setError(null);
  }, []);

  const handleSuggestedQuestion = useCallback((question: string) => {
    setInput(question);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const suggestedQuestions = [
    "Which district has the lowest competition?",
    "What's the average rent in Sukhbaatar district?",
    "Show me investment opportunities",
    "Which areas have high foot traffic?",
    "What's the market growth trend?",
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className={`w-96 max-w-[calc(100vw-2rem)] shadow-2xl transition-all duration-300 border-0 bg-white/95 backdrop-blur-sm dark:bg-gray-900/95 ${
        isMinimized ? "h-16" : "h-[600px] max-h-[calc(100vh-2rem)]"
      }`}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Bot className="h-5 w-5 text-blue-500" />
              {isTyping && (
                <div className="absolute -top-1 -right-1 h-2 w-2 bg-green-500 rounded-full animate-pulse" />
              )}
            </div>
            <CardTitle className="text-sm font-semibold">Oyu-Data-AI Assistant</CardTitle>
            <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
              Beta
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            {messages.length > 1 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearConversation}
                className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900"
                title="Clear conversation"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-6 w-6 p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
              title={isMinimized ? "Maximize" : "Minimize"}
            >
              {isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-6 w-6 p-0 hover:bg-red-100 dark:hover:bg-red-900"
              title="Close chat"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="flex flex-col h-[calc(100%-4rem)] p-4">
            {error && (
              <Alert className="mb-4 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
                <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                <AlertDescription className="text-red-700 dark:text-red-300">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <ScrollArea className="flex-1 pr-4" ref={scrollAreaRef}>
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${message.role === "user" ? "justify-end" : "justify-start"} animate-in slide-in-from-bottom-2 duration-300`}
                  >
                    {message.role === "assistant" && (
                      <div className={`flex h-8 w-8 shrink-0 items-center justify-center rounded-full ${
                        message.isError ? "bg-red-500/10" : "bg-blue-500/10"
                      }`}>
                        {message.isError ? (
                          <AlertCircle className="h-4 w-4 text-red-500" />
                        ) : (
                          <Bot className="h-4 w-4 text-blue-500" />
                        )}
                      </div>
                    )}
                    <div className="flex flex-col max-w-[80%]">
                      <div
                        className={`rounded-lg px-3 py-2 text-sm shadow-sm ${
                          message.role === "user"
                            ? "bg-blue-500 text-white ml-auto"
                            : message.isError
                            ? "bg-red-50 text-red-800 border border-red-200 dark:bg-red-950 dark:text-red-200 dark:border-red-800"
                            : "bg-gray-50 text-gray-900 border border-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700"
                        }`}
                      >
                        {message.content}
                      </div>
                      <div className={`text-xs text-gray-500 mt-1 flex items-center gap-1 ${
                        message.role === "user" ? "justify-end" : "justify-start"
                      }`}>
                        <Clock className="h-3 w-3" />
                        {formatTimestamp(message.timestamp)}
                      </div>
                    </div>
                    {message.role === "user" && (
                      <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-blue-500">
                        <User className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>
                ))}
                {isLoading && (
                  <div className="flex gap-3 justify-start animate-in slide-in-from-bottom-2 duration-300">
                    <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-blue-500/10">
                      <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />
                    </div>
                    <div className="bg-gray-50 border border-gray-200 dark:bg-gray-800 dark:border-gray-700 rounded-lg px-3 py-2 text-sm">
                      <div className="flex space-x-1 items-center">
                        <span className="text-gray-600 dark:text-gray-400">AI is thinking</span>
                        <div className="flex space-x-1">
                          <div className="h-1 w-1 bg-blue-500 rounded-full animate-bounce"></div>
                          <div className="h-1 w-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                          <div className="h-1 w-1 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>

            {messages.length === 1 && (
              <>
                <Separator className="my-4" />
                <div className="space-y-2">
                  <p className="text-xs text-muted-foreground">Try asking:</p>
                  <div className="flex flex-wrap gap-1">
                    {suggestedQuestions.slice(0, 3).map((question) => (
                      <Button
                        key={question}
                        variant="outline"
                        size="sm"
                        className="h-6 text-xs hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-950 dark:hover:text-blue-300"
                        onClick={() => handleSuggestedQuestion(question)}
                      >
                        {question}
                      </Button>
                    ))}
                  </div>
                </div>
                <Separator className="my-4" />
              </>
            )}

            <div className="flex gap-2 items-end">
              <div className="flex-1">
                <Input
                  ref={inputRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Ask about coffee shop market data..."
                  disabled={isLoading}
                  className="resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:focus:border-blue-400"
                  maxLength={1000}
                />
                <div className="text-xs text-gray-500 mt-1 flex justify-between">
                  <span>Press Enter to send</span>
                  <span>{input.length}/1000</span>
                </div>
              </div>
              <Button
                onClick={handleSendMessage}
                disabled={!input.trim() || isLoading}
                size="sm"
                className="px-3 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-700"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
