"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Coffee, Package, Star, TrendingUp, MapPin, Award } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";

import { CoffeeSupplier, CoffeeEquipment, MenuItem } from "./schema";

// Coffee Supplier Columns
export const coffeeSupplierColumns: ColumnDef<CoffeeSupplier>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Supplier" />,
    cell: ({ row }) => {
      const supplier = row.original;
      return (
        <div className="flex items-center gap-2">
          <Coffee className="h-4 w-4 text-muted-foreground" />
          <div className="flex flex-col">
            <div className="font-medium">{supplier.name}</div>
            <div className="text-sm text-muted-foreground flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              {supplier.origin}
            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "tier",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Tier" />,
    cell: ({ row }) => {
      const tier = row.getValue("tier") as string;
      const variant = 
        tier === "Premium Supplier" ? "default" :
        tier === "Standard Supplier" ? "secondary" : "outline";
      
      return <Badge variant={variant}>{tier}</Badge>;
    },
  },
  {
    accessorKey: "qualityGrade",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Quality" />,
    cell: ({ row }) => {
      const grade = row.getValue("qualityGrade") as string;
      const certifications = row.original.certifications;
      
      return (
        <div className="flex flex-col gap-1">
          <Badge variant="outline">{grade}</Badge>
          <div className="flex gap-1">
            {certifications.slice(0, 2).map((cert, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                <Award className="h-3 w-3 mr-1" />
                {cert}
              </Badge>
            ))}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "pricePerKg",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Price/kg" />,
    cell: ({ row }) => {
      const price = row.getValue("pricePerKg") as number;
      return <div className="font-medium">₮{price.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "monthlyVolume",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Volume" />,
    cell: ({ row }) => {
      const volume = row.getValue("monthlyVolume") as number;
      return <div className="text-sm">{volume.toLocaleString()} kg/month</div>;
    },
  },
  {
    accessorKey: "reliability",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Reliability" />,
    cell: ({ row }) => {
      const reliability = row.getValue("reliability") as number;
      const color = 
        reliability >= 90 ? "text-green-600" :
        reliability >= 80 ? "text-blue-600" : "text-yellow-600";
      
      return (
        <div className={`font-medium ${color} flex items-center gap-1`}>
          <Star className="h-3 w-3" />
          {reliability}%
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      const variant = status === "Active" ? "default" : "secondary";
      
      return <Badge variant={variant}>{status}</Badge>;
    },
  },
];

// Coffee Equipment Columns
export const coffeeEquipmentColumns: ColumnDef<CoffeeEquipment>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "item",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Equipment" />,
    cell: ({ row }) => {
      const equipment = row.original;
      return (
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-muted-foreground" />
          <div className="flex flex-col">
            <div className="font-medium">{equipment.item}</div>
            <div className="text-sm text-muted-foreground">{equipment.category}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "supplier",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Supplier" />,
    cell: ({ row }) => {
      const supplier = row.getValue("supplier") as string;
      return <div className="text-sm">{supplier}</div>;
    },
  },
  {
    accessorKey: "price",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Price" />,
    cell: ({ row }) => {
      const price = row.getValue("price") as number;
      return <div className="font-medium text-green-600">₮{price.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "specifications",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Specifications" />,
    cell: ({ row }) => {
      const specs = row.getValue("specifications") as string;
      return <div className="text-sm text-muted-foreground">{specs}</div>;
    },
  },
  {
    accessorKey: "deliveryTime",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Delivery" />,
    cell: ({ row }) => {
      const delivery = row.getValue("deliveryTime") as string;
      return <Badge variant="outline">{delivery}</Badge>;
    },
  },
  {
    accessorKey: "popularity",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Popularity" />,
    cell: ({ row }) => {
      const popularity = row.getValue("popularity") as number;
      return (
        <div className="flex items-center gap-1">
          <TrendingUp className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{popularity}%</span>
        </div>
      );
    },
  },
];

// Menu Item Columns
export const menuItemColumns: ColumnDef<MenuItem>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "item",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Menu Item" />,
    cell: ({ row }) => {
      const item = row.original;
      return (
        <div className="flex items-center gap-2">
          <Coffee className="h-4 w-4 text-muted-foreground" />
          <div className="flex flex-col">
            <div className="font-medium">{item.item}</div>
            <div className="text-sm text-muted-foreground">{item.category}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "price",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Price" />,
    cell: ({ row }) => {
      const price = row.getValue("price") as number;
      return <div className="font-medium">₮{price.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "cost",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Cost" />,
    cell: ({ row }) => {
      const cost = row.getValue("cost") as number;
      return <div className="text-sm text-muted-foreground">₮{cost.toLocaleString()}</div>;
    },
  },
  {
    accessorKey: "margin",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Margin" />,
    cell: ({ row }) => {
      const margin = row.getValue("margin") as number;
      const color = margin >= 70 ? "text-green-600" : margin >= 60 ? "text-blue-600" : "text-yellow-600";
      
      return <div className={`font-medium ${color}`}>{margin}%</div>;
    },
  },
  {
    accessorKey: "popularity",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Popularity" />,
    cell: ({ row }) => {
      const popularity = row.getValue("popularity") as number;
      return (
        <div className="flex items-center gap-1">
          <TrendingUp className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{popularity}%</span>
        </div>
      );
    },
  },
  {
    accessorKey: "seasonal",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Seasonal" />,
    cell: ({ row }) => {
      const seasonal = row.getValue("seasonal") as boolean;
      return (
        <Badge variant={seasonal ? "secondary" : "outline"}>
          {seasonal ? "Seasonal" : "Year-round"}
        </Badge>
      );
    },
  },
];
