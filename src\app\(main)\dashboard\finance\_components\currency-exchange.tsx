"use client";

import { TrendingUp, TrendingDown } from "lucide-react";

import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import { currencyRates } from "./mongolia-market-data";

export function CurrencyExchange() {
  const currencies = [
    { code: "USD", rate: currencyRates.USD, change: 2.5 },
    { code: "EUR", rate: currencyRates.EUR, change: -1.2 },
    { code: "CNY", rate: currencyRates.CNY, change: 0.8 },
    { code: "KRW", rate: currencyRates.KRW, change: 1.5 },
    { code: "RUB", rate: currencyRates.RUB, change: -3.2 },
    { code: "JPY", rate: currencyRates.JPY, change: 0.3 }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>MNT Exchange Rates</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {currencies.map((currency) => (
            <div key={currency.code} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="font-medium">{currency.code}</div>
                <div className="text-sm text-muted-foreground">
                  1 {currency.code} = ₮{currency.rate.toLocaleString()}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className={`flex items-center gap-1 text-sm ${
                  currency.change >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {currency.change >= 0 ? (
                    <TrendingUp className="h-3 w-3" />
                  ) : (
                    <TrendingDown className="h-3 w-3" />
                  )}
                  {Math.abs(currency.change)}%
                </div>
              </div>
            </div>
          ))}
        </div>
        <Separator />
        <div className="text-xs text-muted-foreground text-center">
          Last updated: Today, 14:30 (Bank of Mongolia)
        </div>
      </CardContent>
    </Card>
  );
}
