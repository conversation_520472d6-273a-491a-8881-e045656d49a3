"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Coffee, MapPin, TrendingUp, Star, Building2 } from "lucide-react";
import { z } from "zod";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";

import { coffeeMarketSchema } from "./coffee-market-schema";

export const coffeeMarketColumns: ColumnDef<z.infer<typeof coffeeMarketSchema>>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center justify-center">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "shopName",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Coffee Shop" />,
    cell: ({ row }) => {
      const shop = row.original;
      return (
        <div className="flex items-center gap-2">
          <Coffee className="h-4 w-4 text-muted-foreground" />
          <div className="flex flex-col">
            <div className="font-medium">{shop.shopName}</div>
            <div className="text-sm text-muted-foreground flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              {shop.district}
            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "type",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Business Type" />,
    cell: ({ row }) => {
      const type = row.getValue("type") as string;
      const variant = 
        type === "International Chain" ? "default" :
        type === "Local Chain" ? "secondary" : "outline";
      
      return (
        <div className="flex items-center gap-2">
          <Building2 className="h-3 w-3 text-muted-foreground" />
          <Badge variant={variant}>{type}</Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Performance Status" />,
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      const variant = 
        status === "Market Leader" || status === "Excellent" ? "default" :
        status === "High Performance" ? "secondary" :
        status === "Good Performance" || status === "Growing" ? "outline" : "secondary";
      
      return (
        <Badge variant={variant} className="flex items-center gap-1">
          <Star className="h-3 w-3" />
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: "monthlyRevenue",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Monthly Revenue" />,
    cell: ({ row }) => {
      const revenue = row.getValue("monthlyRevenue") as number;
      return (
        <div className="font-medium text-green-600">
          ₮{(revenue / 1000000).toFixed(1)}M
        </div>
      );
    },
  },
  {
    accessorKey: "investmentScore",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Investment Score" />,
    cell: ({ row }) => {
      const score = row.getValue("investmentScore") as number;
      const color = 
        score >= 90 ? "text-green-600" :
        score >= 75 ? "text-blue-600" :
        score >= 60 ? "text-yellow-600" : "text-red-600";
      
      return (
        <div className={`font-medium ${color} flex items-center gap-1`}>
          <TrendingUp className="h-3 w-3" />
          {score}/100
        </div>
      );
    },
  },
  {
    accessorKey: "marketAnalyst",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Market Analyst" />,
    cell: ({ row }) => {
      const analyst = row.getValue("marketAnalyst") as string;
      return <div className="text-sm text-muted-foreground">{analyst}</div>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const shop = row.original;
      return (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm">
            View Analysis
          </Button>
          <Button variant="ghost" size="sm">
            <TrendingUp className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];
