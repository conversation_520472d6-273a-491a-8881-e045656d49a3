"use client";

import React, { useState } from "react";
import { MessageCircle, X } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { AIChatbot } from "./ai-chatbot";

export function ChatbotTrigger() {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <>
      {!isOpen && (
        <div className="fixed bottom-4 right-4 z-50">
          <Button
            onClick={handleToggle}
            className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 bg-blue-500 hover:bg-blue-600"
            size="icon"
          >
            <MessageCircle className="h-6 w-6" />
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              AI
            </Badge>
          </Button>
        </div>
      )}
      
      <AIChatbot isOpen={isOpen} onToggle={handleToggle} onClose={handleClose} />
    </>
  );
}
