"use client";

import * as React from "react";

import { Plus } from "lucide-react";
import { z } from "zod";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useDataTableInstance } from "@/hooks/use-data-table-instance";

import { DataTable as DataTableNew } from "../../../../../components/data-table/data-table";
import { DataTablePagination } from "../../../../../components/data-table/data-table-pagination";
import { DataTableViewOptions } from "../../../../../components/data-table/data-table-view-options";
import { withDndColumn } from "../../../../../components/data-table/table-utils";

import { coffeeMarketColumns } from "./coffee-market-columns";
import { coffeeMarketSchema } from "./coffee-market-schema";

export function CoffeeMarketTable({ data: initialData }: { data: z.infer<typeof coffeeMarketSchema>[] }) {
  const [data, setData] = React.useState(() => initialData);
  const columns = withDndColumn(coffeeMarketColumns);
  const table = useDataTableInstance({ data, columns, getRowId: (row) => row.id.toString() });

  // Filter data by district
  const sukhbaatarData = data.filter(shop => shop.district === "Sukhbaatar");
  const chingelteiData = data.filter(shop => shop.district === "Chingeltei");
  const bayanzurkhData = data.filter(shop => shop.district === "Bayanzurkh");
  const khanUulData = data.filter(shop => shop.district === "Khan-Uul");
  const songinoData = data.filter(shop => shop.district === "Songinokhairkhan");

  // Filter data by type
  const internationalChains = data.filter(shop => shop.type === "International Chain");
  const localChains = data.filter(shop => shop.type === "Local Chain");
  const independentShops = data.filter(shop => shop.type === "Independent");

  // Calculate summary statistics
  const totalRevenue = data.reduce((sum, shop) => sum + shop.monthlyRevenue, 0);
  const avgInvestmentScore = data.reduce((sum, shop) => sum + shop.investmentScore, 0) / data.length;
  const topPerformers = data.filter(shop => shop.investmentScore >= 80).length;

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">Coffee Market Intelligence Analysis</h2>
        <p className="text-muted-foreground">
          Comprehensive performance metrics, investment scores, and market analysis for coffee shops across Ulaanbaatar districts
        </p>
      </div>

      {/* Coffee Market Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-card rounded-lg border p-4">
          <div className="text-sm text-muted-foreground">Total Coffee Shops</div>
          <div className="text-2xl font-bold">{data.length}</div>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <div className="text-sm text-muted-foreground">Combined Monthly Revenue</div>
          <div className="text-2xl font-bold text-green-600">₮{(totalRevenue / 1000000).toFixed(1)}M</div>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <div className="text-sm text-muted-foreground">Avg Investment Score</div>
          <div className="text-2xl font-bold text-blue-600">{avgInvestmentScore.toFixed(1)}/100</div>
        </div>
        <div className="bg-card rounded-lg border p-4">
          <div className="text-sm text-muted-foreground">High Performers</div>
          <div className="text-2xl font-bold text-purple-600">{topPerformers}</div>
        </div>
      </div>

      <Tabs defaultValue="all-shops" className="w-full flex-col justify-start gap-6">
      <div className="flex items-center justify-between">
        <Label htmlFor="view-selector" className="sr-only">
          View
        </Label>
        <Select defaultValue="all-shops">
          <SelectTrigger className="flex w-fit @4xl/main:hidden" size="sm" id="view-selector">
            <SelectValue placeholder="Select a view" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all-shops">All Coffee Shops</SelectItem>
            <SelectItem value="by-district">By District</SelectItem>
            <SelectItem value="by-type">By Business Type</SelectItem>
            <SelectItem value="investment-analysis">Investment Analysis</SelectItem>
          </SelectContent>
        </Select>
        <TabsList className="**:data-[slot=badge]:bg-muted-foreground/30 hidden **:data-[slot=badge]:size-5 **:data-[slot=badge]:rounded-full **:data-[slot=badge]:px-1 @4xl/main:flex">
          <TabsTrigger value="all-shops">All Coffee Shops</TabsTrigger>
          <TabsTrigger value="by-district">
            By District <Badge variant="secondary">5</Badge>
          </TabsTrigger>
          <TabsTrigger value="by-type">
            By Type <Badge variant="secondary">3</Badge>
          </TabsTrigger>
          <TabsTrigger value="investment-analysis">Investment Analysis</TabsTrigger>
        </TabsList>
        <div className="flex items-center gap-2">
          <DataTableViewOptions table={table} />
          <Button variant="outline" size="sm">
            <Plus />
            <span className="hidden lg:inline">Add Coffee Shop</span>
          </Button>
        </div>
      </div>
      
      <TabsContent value="all-shops" className="relative flex flex-col gap-4 overflow-auto">
        <div className="overflow-hidden rounded-lg border">
          <DataTableNew dndEnabled table={table} columns={columns} onReorder={setData} />
        </div>
        <DataTablePagination table={table} />
      </TabsContent>
      
      <TabsContent value="by-district" className="flex flex-col gap-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="space-y-2">
            <h3 className="font-semibold">Sukhbaatar District ({sukhbaatarData.length})</h3>
            <div className="text-sm text-muted-foreground">
              Avg Revenue: ₮{(sukhbaatarData.reduce((sum, shop) => sum + shop.monthlyRevenue, 0) / sukhbaatarData.length / 1000000).toFixed(1)}M
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold">Chingeltei District ({chingelteiData.length})</h3>
            <div className="text-sm text-muted-foreground">
              Avg Revenue: ₮{(chingelteiData.reduce((sum, shop) => sum + shop.monthlyRevenue, 0) / chingelteiData.length / 1000000).toFixed(1)}M
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold">Other Districts ({bayanzurkhData.length + khanUulData.length + songinoData.length})</h3>
            <div className="text-sm text-muted-foreground">
              Combined Revenue: ₮{((bayanzurkhData.reduce((sum, shop) => sum + shop.monthlyRevenue, 0) + 
                khanUulData.reduce((sum, shop) => sum + shop.monthlyRevenue, 0) + 
                songinoData.reduce((sum, shop) => sum + shop.monthlyRevenue, 0)) / 1000000).toFixed(1)}M
            </div>
          </div>
        </div>
        <div className="overflow-hidden rounded-lg border">
          <DataTableNew dndEnabled table={table} columns={columns} onReorder={setData} />
        </div>
        <DataTablePagination table={table} />
      </TabsContent>
      
      <TabsContent value="by-type" className="flex flex-col gap-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <h3 className="font-semibold">International Chains ({internationalChains.length})</h3>
            <div className="text-sm text-muted-foreground">
              Avg Score: {(internationalChains.reduce((sum, shop) => sum + shop.investmentScore, 0) / internationalChains.length).toFixed(1)}/100
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold">Local Chains ({localChains.length})</h3>
            <div className="text-sm text-muted-foreground">
              Avg Score: {(localChains.reduce((sum, shop) => sum + shop.investmentScore, 0) / localChains.length).toFixed(1)}/100
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="font-semibold">Independent Shops ({independentShops.length})</h3>
            <div className="text-sm text-muted-foreground">
              Avg Score: {(independentShops.reduce((sum, shop) => sum + shop.investmentScore, 0) / independentShops.length).toFixed(1)}/100
            </div>
          </div>
        </div>
        <div className="overflow-hidden rounded-lg border">
          <DataTableNew dndEnabled table={table} columns={columns} onReorder={setData} />
        </div>
        <DataTablePagination table={table} />
      </TabsContent>
      
      <TabsContent value="investment-analysis" className="flex flex-col">
        <div className="aspect-video w-full flex-1 rounded-lg border border-dashed flex items-center justify-center">
          <div className="text-center">
            <h3 className="text-lg font-semibold">Investment Analysis Dashboard</h3>
            <p className="text-muted-foreground">Advanced analytics for coffee shop investment opportunities</p>
            <p className="text-sm text-muted-foreground mt-2">ROI predictions, market trends, and risk assessment</p>
          </div>
        </div>
      </TabsContent>
    </Tabs>
    </div>
  );
}
